"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/logbook/forms/tasking.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Tasking; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./vessel-rescue-fields */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\");\n/* harmony import */ var _person_rescue_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./person-rescue-field */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue-field.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _risk_analysis__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _barrel_optimize_names_SquareArrowOutUpRight_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=SquareArrowOutUpRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square-arrow-out-up-right.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons/SealogsFuelIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsFuelIcon.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/fuelTank */ \"(app-pages-browser)/./src/app/offline/models/fuelTank.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/fuelLog */ \"(app-pages-browser)/./src/app/offline/models/fuelLog.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Combobox is already imported from '@/components/ui/comboBox'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Tasking(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, inLogbook = false, previousDropEvent, vessel, members, locked, offline = false, fuelLogs } = param;\n    var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents, _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1, _currentTrip_tripEvents_nodes_find2, _currentTrip_tripEvents2, _currentTrip_tripEvents_nodes_find3, _currentTrip_tripEvents3, _currentTrip_tripEvents_nodes_find4, _currentTrip_tripEvents4, _currentTrip_tripEvents_nodes_find5, _currentTrip_tripEvents5;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\"));\n    const [tasking, setTasking] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openRiskAnalysis, setOpenRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [pauseGroup, setPauseGroup] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openTaskID, setOpenTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [completedTaskID, setCompletedTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [towingChecklistID, setTowingChecklistID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [groupID, setGroupID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentIncident, setCurrentIncident] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [taskingPausedValue, setTaskingPausedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingResumedValue, setTaskingResumedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingCompleteValue, setTaskingCompleteValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [locationDescription, setLocationDescription] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // const [members, setMembers] = useState<any>(false)\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankList, setFuelTankList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankBuffer, setFuelTankBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [updatedFuelLogs, setUpdatedFuelLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const fuelTankModel = new _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__[\"default\"]();\n    const fuelLogModel = new _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryGetFuelTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELTANKS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readFuelTanks.nodes;\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelTanks error\", error);\n        }\n    });\n    const getFuelTanks = async (fuelTankIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelTankIds);\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        } else {\n            await queryGetFuelTanks({\n                variables: {\n                    id: fuelTankIds\n                }\n            });\n        }\n    };\n    const handleSetVessel = (vessel)=>{\n        var _vessel_parentComponent_Components;\n        const fuelTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components === void 0 ? void 0 : _vessel_parentComponent_Components.nodes.filter((item)=>item.basicComponent.componentCategory === \"FuelTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (vessel) {\n            handleSetVessel(vessel);\n        }\n    }, [\n        vessel\n    ]);\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"HH:mm\"));\n    };\n    const offlineGetPreviousDropEvent = async ()=>{\n        const event = await tripEventModel.getById(previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id);\n        if (event) {\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n            if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                setCurrentLocation({\n                    latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                    longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        } else {\n            if ((previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id) > 0) {\n                if (offline) {\n                    offlineGetPreviousDropEvent();\n                } else {\n                    getPreviousDropEvent({\n                        variables: {\n                            id: previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const handleTaskingPauseChange = (selectedTask)=>{\n        setPauseGroup(selectedTask.value);\n        setTaskingPausedValue(selectedTask);\n    };\n    const [getPreviousDropEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n                setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n                if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                    var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                        longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                    });\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting previous event\", error);\n        }\n    });\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            var _currentTrip_tripEvents;\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                // eventType_TaskingID\n                if (!event.eventType_Tasking) {\n                    const eventType_Tasking = await taskingModel.getById(event.eventType_TaskingID);\n                    event.eventType_Tasking = eventType_Tasking;\n                }\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _currentTrip_tripEvents;\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations([\n                {\n                    label: \"--- Add new location ---\",\n                    value: \"newLocation\"\n                },\n                ...geoLocations.filter((location)=>location.title).map((location)=>({\n                        label: location.title,\n                        value: location.id,\n                        latitude: location.lat,\n                        longitude: location.long\n                    }))\n            ]);\n        }\n    }, [\n        geoLocations\n    ]);\n    const handleSave = async function() {\n        let vesselRescueID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, personRescueID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const variables = {\n            input: {\n                geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                time: time,\n                title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                type: type,\n                operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                vesselRescueID: vesselRescueID > 0 ? vesselRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.vesselRescueID,\n                personRescueID: personRescueID > 0 ? personRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.personRescueID,\n                currentEntryID: currentTrip.id,\n                tripEventID: tasking === null || tasking === void 0 ? void 0 : tasking.id,\n                pausedTaskID: +pauseGroup,\n                openTaskID: +openTaskID,\n                completedTaskID: +completedTaskID,\n                comments: content,\n                groupID: +groupID,\n                status: \"Open\",\n                cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : null,\n                sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : null\n            }\n        };\n        if (pauseGroup > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +pauseGroup,\n                    status: \"Paused\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +pauseGroup,\n                            status: \"Paused\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (openTaskID > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +openTaskID,\n                    status: \"Open\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +openTaskID,\n                            status: \"Open\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (completedTaskID > 0 && !currentEvent) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +completedTaskID > 0 ? +completedTaskID : (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    status: \"Completed\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +completedTaskID,\n                            status: \"Completed\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (currentEvent) {\n            if (offline) {\n                const data = await taskingModel.save({\n                    ...variables.input,\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                    tripEventID: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id)\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n                await getCurrentEvent(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                            ...variables.input\n                        }\n                    }\n                });\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            }\n        } else {\n            if (offline) {\n                const newID = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)();\n                await tripEventModel.save({\n                    id: newID,\n                    eventCategory: \"Tasking\",\n                    eventType_TaskingID: +newID,\n                    logBookEntrySectionID: currentTrip.id\n                });\n                const data = await taskingModel.save({\n                    id: +newID,\n                    geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                    time: time,\n                    title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                    fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                    type: type,\n                    operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                    lat: currentLocation.latitude.toString(),\n                    long: currentLocation.longitude.toString(),\n                    vesselRescueID: vesselRescueID,\n                    personRescueID: personRescueID,\n                    currentEntryID: currentTrip.id,\n                    pausedTaskID: +pauseGroup,\n                    openTaskID: +openTaskID,\n                    completedTaskID: +completedTaskID,\n                    comments: content,\n                    groupID: +groupID,\n                    status: \"Open\",\n                    cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                    sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false),\n                    tripEventID: newID\n                });\n                updateFuelLogs(+data.id);\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n            } else {\n                createEventType_Tasking({\n                    variables: {\n                        input: {\n                            geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                            time: time,\n                            title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                            fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                            type: type,\n                            operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                            lat: currentLocation.latitude.toString(),\n                            long: currentLocation.longitude.toString(),\n                            vesselRescueID: vesselRescueID,\n                            personRescueID: personRescueID,\n                            currentEntryID: currentTrip.id,\n                            pausedTaskID: +pauseGroup,\n                            openTaskID: +openTaskID,\n                            completedTaskID: +completedTaskID,\n                            comments: content,\n                            groupID: +groupID,\n                            status: \"Open\",\n                            cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                            sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false)\n                        }\n                    }\n                });\n            }\n        }\n        setCompletedTaskID(false);\n        setOpenTaskID(false);\n        setPauseGroup(false);\n    };\n    const [createEventType_Tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CreateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_Tasking;\n            updateFuelLogs(+data.id);\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.message);\n        }\n    });\n    const [updateEventType_tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_tasking;\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating activity type tasking\", error);\n        }\n    });\n    const handleOperationTypeChange = (selectedOperation)=>{\n        if (selectedOperation.value === \"newLocation\") {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.loading(\"Getting your current location... Please wait...\");\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Location found\");\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Geolocation is not supported by your browser\");\n                setOpenNewLocationDialog(true);\n            }\n        } else {\n            setTasking({\n                ...tasking,\n                operationType: selectedOperation.value\n            });\n        }\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            // Update tripEvent to clear location data\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    geoLocationID: 0,\n                    lat: null,\n                    long: null\n                }\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.loading(\"Getting your current location... Please wait...\");\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Geolocation is not supported by your browser\");\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTasking({\n                ...tasking,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n            // Update tripEvent to reflect the selected location\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    geoLocationID: +value.value,\n                    lat: null,\n                    long: null\n                }\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n            // Update tripEvent to reflect the new coordinates so LocationField can display them\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    lat: value.latitude,\n                    long: value.longitude,\n                    geoLocationID: 0\n                }\n            });\n        }\n    };\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTasking({\n                    ...tasking,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTasking({\n                ...tasking,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Error creating GeoLocation\");\n            console.error(\"Error creating GeoLocation: \" + error.message);\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    const handleParentLocationChange = (selectedOption)=>{\n        if (selectedOption) {\n            setParentLocation(selectedOption.value);\n        } else {\n            setParentLocation(null);\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleTaskingGroupChange = (selectedGroup)=>{\n        setGroupID(selectedGroup.value);\n        setOpenTaskID(selectedGroup.value);\n        setTaskingResumedValue(selectedGroup);\n    };\n    const handleTaskingCompleteChange = (selectedGroup)=>{\n        setCompletedTaskID(selectedGroup.value);\n        setTaskingCompleteValue(selectedGroup);\n    };\n    const operationTypes = [\n        {\n            label: \"Vessel Mechanical / equipment failure\",\n            value: \"Vessel Mechanical or equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Vessel - other\",\n            value: \"Vessel - other\"\n        },\n        {\n            label: \"Person in water\",\n            value: \"Person in water\"\n        },\n        {\n            label: \"Person lost / missing\",\n            value: \"Person lost or missing\"\n        },\n        {\n            label: \"Suicide\",\n            value: \"Suicide\"\n        },\n        {\n            label: \"Medical condition\",\n            value: \"Medical condition\"\n        },\n        {\n            label: \"Person - other\",\n            value: \"Person - other\"\n        }\n    ];\n    const goSetTaskingTitle = (event)=>{\n        let title = \"\";\n        if (event && event.eventType_Tasking.type === \"TaskingStartUnderway\") {\n            title = event.eventType_Tasking.title;\n        }\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(tasking.title)) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(title))) {\n            setTasking({\n                ...tasking,\n                title: title\n            });\n        }\n    };\n    const findPreviousEvent = (selectedEvent)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            if (selectedEvent) {\n                if (selectedEvent.eventType_Tasking.vesselRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.vesselRescueID === selectedEvent.eventType_Tasking.vesselRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n                if (selectedEvent.eventType_Tasking.personRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.personRescueID === selectedEvent.eventType_Tasking.personRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n            }\n            goSetTaskingTitle(prevEvent);\n            return prevEvent;\n        }\n        if (type === \"TaskingComplete\") {\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents1;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            } else {\n                const res = prevEvent ? prevEvent : selectedEvent;\n                goSetTaskingTitle(res);\n                return res;\n            }\n        }\n        goSetTaskingTitle(selectedEvent);\n        return selectedEvent;\n    };\n    const findPreviousRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.vesselRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const findPreviousHumanRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.personRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const currentOperationTypeLabel = (label)=>{\n        return label ? label : \"-- Select operation type --\";\n    };\n    const currentOperationTypeValue = (value)=>{\n        return value;\n    };\n    const getPreviousSAROP = (sarop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"cgop\") {\n            return \"\";\n        }\n        if (currentIncident === \"sarop\") {\n            return sarop ? sarop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.sarop) ? e.eventType_Tasking.sarop : sarop ? sarop : \"\";\n    };\n    const getPreviousCGOP = (cgop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"sarop\") {\n            return \"\";\n        }\n        if (currentIncident === \"cgop\") {\n            return cgop ? cgop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.cgop) ? e.eventType_Tasking.cgop : cgop ? cgop : \"\";\n    };\n    const getIsSAROP = (sarop)=>{\n        var _tasking_cgop, _tasking_sarop;\n        return currentIncident === \"sarop\" || currentIncident !== \"sarop\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(sarop)) && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_cgop = tasking === null || tasking === void 0 ? void 0 : tasking.cgop) !== null && _tasking_cgop !== void 0 ? _tasking_cgop : \"\")) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_sarop = tasking === null || tasking === void 0 ? void 0 : tasking.sarop) !== null && _tasking_sarop !== void 0 ? _tasking_sarop : \"\"));\n    };\n    const getIsCGOP = (cgop)=>{\n        var _tasking_sarop, _tasking_cgop;\n        return currentIncident === \"cgop\" || currentIncident !== \"cgop\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(cgop)) && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_sarop = tasking === null || tasking === void 0 ? void 0 : tasking.sarop) !== null && _tasking_sarop !== void 0 ? _tasking_sarop : \"\")) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_cgop = tasking === null || tasking === void 0 ? void 0 : tasking.cgop) !== null && _tasking_cgop !== void 0 ? _tasking_cgop : \"\"));\n    };\n    const getPreviousFuelLevel = (fuelLevel)=>{\n        var _selectedEvent_eventType_Tasking, _currentTrip_tripEvents;\n        if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.fuelLevel) > 0) {\n            var _selectedEvent_eventType_Tasking1;\n            return selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking1.fuelLevel;\n        }\n        if (fuelLevel || (tasking === null || tasking === void 0 ? void 0 : tasking.updatedFuelLevel)) {\n            return fuelLevel;\n        }\n        const fuelLevels = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>event.eventType_Tasking.fuelLevel > 0).map((event)=>event.eventType_Tasking.fuelLevel);\n        const minFuelLevel = (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? fuelLevels[fuelLevels.length - 1] : fuelLevel;\n        return (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? minFuelLevel : fuelLevel ? fuelLevel : \"\";\n    };\n    const getPreviousTask = (task)=>{\n        var _prevEvent_eventType_Tasking, _prevEvent_eventType_Tasking1, _prevEvent_eventType_Tasking2;\n        if (task) {\n            return task;\n        }\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _prevEvent_eventType_Tasking3, _prevEvent_eventType_Tasking4, _prevEvent_eventType_Tasking5, _prevEvent_eventType_Tasking6;\n            setCompletedTaskID(prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking3 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking3 === void 0 ? void 0 : _prevEvent_eventType_Tasking3.id);\n            setTaskingCompleteValue({\n                label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking4 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking4 === void 0 ? void 0 : _prevEvent_eventType_Tasking4.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking5 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking5 === void 0 ? void 0 : _prevEvent_eventType_Tasking5.title),\n                value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking6 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking6 === void 0 ? void 0 : _prevEvent_eventType_Tasking6.id\n            });\n        }\n        return prevEvent ? {\n            label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking === void 0 ? void 0 : _prevEvent_eventType_Tasking.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking1 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking1 === void 0 ? void 0 : _prevEvent_eventType_Tasking1.title),\n            value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking2 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking2 === void 0 ? void 0 : _prevEvent_eventType_Tasking2.id\n        } : task;\n    };\n    const isVesselRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.vesselRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID) > 0;\n    };\n    const isPersonRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.personRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID) > 0;\n    };\n    const displayVessesRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isVesselRescue() || type === \"TaskingOnTow\" && isVesselRescue() || type === \"TaskingComplete\" && isVesselRescue() || tasking.operationType === \"Vessel Mechanical or equipment failure\" || tasking.operationType === \"Vessel adrift\" || tasking.operationType === \"Vessel aground\" || tasking.operationType === \"Capsize\" || tasking.operationType === \"Vessel requiring tow\" || tasking.operationType === \"Flare sighting\" || tasking.operationType === \"Vessel sinking\" || tasking.operationType === \"Collision\" || tasking.operationType === \"Vessel overdue\" || tasking.operationType === \"Vessel - other\" || +tasking.lat > 0 && +tasking.long > 0) {\n            return true;\n        }\n        return false;\n    };\n    const displayPersonRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isPersonRescue() || type === \"TaskingOnTow\" && isPersonRescue() || type === \"TaskingComplete\" && isPersonRescue() || tasking.operationType === \"Person in water\" || tasking.operationType === \"Person lost or missing\" || tasking.operationType === \"Suicide\" || tasking.operationType === \"Medical condition\" || tasking.operationType === \"Person - other\") {\n            return true;\n        }\n        return false;\n    };\n    const handleSaropChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"sarop\");\n        }\n    };\n    const handleCgopChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"cgop\");\n        }\n    };\n    const handleUpdateFuelTank = (tank, value)=>{\n        if (tank.capacity < +value) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Fuel level cannot be higher than tank capacity of \" + tank.capacity);\n            return;\n        }\n        setFuelTankList(fuelTankList.map((item)=>{\n            if (item.id === tank.id) {\n                return {\n                    ...item,\n                    currentLevel: +value\n                };\n            }\n            return item;\n        }));\n        setTasking({\n            ...tasking,\n            fuelLog: false\n        });\n        if (fuelTankBuffer.length > 0 && fuelTankBuffer.filter((item)=>item.tankID === tank.id)) {\n            setFuelTankBuffer(fuelTankBuffer.map((item)=>{\n                if (item.tankID === tank.id) {\n                    return {\n                        ...item,\n                        value: +value\n                    };\n                }\n                return item;\n            }));\n        } else {\n            setFuelTankBuffer([\n                ...fuelTankBuffer,\n                {\n                    tankID: tank.id,\n                    value: +value\n                }\n            ]);\n        }\n    };\n    // Create a debounced version of the update function\n    // const handleUpdateFuelTank = useCallback(\n    //     debounce((tank: any, value: any) => {\n    //         updateFuelTankValue(tank, value)\n    //     }, 500), // 500ms delay\n    //     [fuelTankList, tasking],\n    // )\n    const [updateFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UPDATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel log\", error);\n        }\n    });\n    const [createFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.createFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error creating fuel log\", error);\n        }\n    });\n    const [updateFuelTank] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateFuelTank, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelTank;\n            const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === data.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n            if (fuelLog) {\n                updateFuelLog({\n                    variables: {\n                        input: {\n                            id: fuelLog.id,\n                            fuelAfter: +fuelLog.fuelAfter\n                        }\n                    }\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel tank\", error);\n        }\n    });\n    const updateFuelLogs = function() {\n        let currentID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        if (fuelTankList) {\n            Promise.all(fuelTankList === null || fuelTankList === void 0 ? void 0 : fuelTankList.map(async (fuelTank)=>{\n                const variables = {\n                    input: {\n                        id: fuelTank.id,\n                        currentLevel: fuelTank.currentLevel\n                    }\n                };\n                if (!currentEvent) {\n                    if (offline) {\n                        await fuelTankModel.save({\n                            id: fuelTank.id,\n                            currentLevel: fuelTank.currentLevel\n                        });\n                    } else {\n                        updateFuelTank({\n                            variables: variables\n                        });\n                    }\n                }\n                if (currentEvent) {\n                    if (offline) {\n                        var _currentEvent_eventType_Tasking_fuelLog_nodes_find;\n                        await fuelLogModel.save({\n                            id: ((_currentEvent_eventType_Tasking_fuelLog_nodes_find = currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>{\n                                var _log_fuelTank;\n                                ((_log_fuelTank = log.fuelTank) === null || _log_fuelTank === void 0 ? void 0 : _log_fuelTank.id) === fuelTank.id;\n                            })) === null || _currentEvent_eventType_Tasking_fuelLog_nodes_find === void 0 ? void 0 : _currentEvent_eventType_Tasking_fuelLog_nodes_find.id) || (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        updateFuelLog({\n                            variables: {\n                                input: {\n                                    id: currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>log.fuelTank.id === fuelTank.id).id,\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                } else {\n                    if (offline) {\n                        await fuelLogModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        createFuelLog({\n                            variables: {\n                                input: {\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                }\n            }));\n        }\n    };\n    const getInitialFuelLevel = (tank)=>{\n        if (fuelTankBuffer.length > 0) {\n            const fuelTank = fuelTankBuffer.find((item)=>item.tankID === tank.id);\n            if (fuelTank) {\n                return fuelTank.value;\n            }\n        }\n        if (tripReport.length > 0) {\n            var _fuelLogs_filter_sort;\n            const fuelLogs = tripReport.map((trip)=>{\n                return trip.tripEvents.nodes.filter((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes.length > 0 || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes.length > 0 || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes.length > 0).flatMap((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes);\n            }).flat();\n            const lastFuelLog = fuelLogs === null || fuelLogs === void 0 ? void 0 : (_fuelLogs_filter_sort = fuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>b.id - a.id)) === null || _fuelLogs_filter_sort === void 0 ? void 0 : _fuelLogs_filter_sort[0];\n            if (lastFuelLog) {\n                return lastFuelLog.fuelAfter;\n            }\n        }\n        // if (\n        //     currentTrip &&\n        //     currentTrip?.tripEvents?.nodes?.length > 0 &&\n        //     currentTrip.tripEvents.nodes.find(\n        //         (event: any) =>\n        //             event.eventCategory === 'Tasking' &&\n        //             event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //     )\n        // ) {\n        //     const fuelLog = currentTrip.tripEvents.nodes\n        //         .filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (fuelLog) {\n        //         return fuelLog.fuelAfter\n        //     }\n        // }\n        // if (tripReport && tripReport.length > 1) {\n        //     const latestTripFuelLog = tripReport\n        //         .filter((trip: any) => trip.id < currentTrip.id)\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.tripEvents?.nodes.filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (latestTripFuelLog) {\n        //         return latestTripFuelLog.fuelAfter\n        //     }\n        // }\n        const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n        return fuelLog ? +tank.capacity > +fuelLog.fuelAfter ? +fuelLog.fuelAfter : +tank.capacity : +tank.currentLevel;\n    };\n    const getFuelLogs = async (fuelLogIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelLogIds);\n        } else {\n            await queryGetFuelLogs({\n                variables: {\n                    id: fuelLogIds\n                }\n            });\n        }\n    };\n    const [queryGetFuelLogs] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELLOGS, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.readFuelLogs.nodes;\n            setUpdatedFuelLogs(data);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelLogs error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFuelLogs(fuelLogs.map((item)=>item.id));\n    }, []);\n    var _getPreviousCGOP, _getPreviousSAROP;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !inLogbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 text-sm font-semibold uppercase\",\n                        children: [\n                            type === \"TaskingStartUnderway\" && \"Tasking start / underway\",\n                            type === \"TaskingPaused\" && \"Tasking paused\",\n                            type === \"TaskingResumed\" && \"Tasking resumed\",\n                            type === \"TaskingOnScene\" && tasking.title,\n                            type === \"TaskingOnTow\" && tasking.title,\n                            type === \"TaskingComplete\" && tasking.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1653,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem] mb-2\",\n                        children: [\n                            \"Give this tasking a title and choose an operation type.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1665,\n                                columnNumber: 21\n                            }, this),\n                            \"Recording fuel levels goes toward\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"fuel reports for allocating to different operations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1667,\n                                columnNumber: 21\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1663,\n                        columnNumber: 17\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem]\",\n                        children: \"Utilise attached checklist to ensure towing procedure is followed and any risks identified.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1673,\n                        columnNumber: 21\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_29__.CheckFieldLabel, {\n                        type: \"checkbox\",\n                        checked: allChecked,\n                        className: \"w-fit\",\n                        variant: \"success\",\n                        rightContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SquareArrowOutUpRight_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 1685,\n                            columnNumber: 29\n                        }, void 0),\n                        onClick: ()=>{\n                            setOpenRiskAnalysis(true);\n                        },\n                        label: \"Towing checklist - risk analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1679,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Time when tasking takes place\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1696,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        time: time,\n                                        handleTimeChange: handleTimeChange,\n                                        timeID: \"time\",\n                                        fieldName: \"Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1697,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1694,\n                                columnNumber: 21\n                            }, this),\n                            type === \"TaskingStartUnderway\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Title of tasking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1707,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                        id: \"title\",\n                                        name: \"title\",\n                                        type: \"text\",\n                                        value: (tasking === null || tasking === void 0 ? void 0 : tasking.title) ? tasking.title : \"\",\n                                        placeholder: \"Title\",\n                                        onChange: (e)=>{\n                                            setTasking({\n                                                ...tasking,\n                                                title: e.target.value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1708,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1705,\n                                columnNumber: 25\n                            }, this),\n                            fuelTankList && fuelTankList.map((tank)=>/*#__PURE__*/ {\n                                var _tasking_fuelLog_find;\n                                var _tank_currentLevel, _ref;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" flex flex-col gap-2 my-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__.SealogsFuelIcon, {\n                                                    className: \"size-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1729,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                    children: tank.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1730,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1728,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                            type: \"number\",\n                                            placeholder: \"Fuel end\",\n                                            value: (_ref = (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : (tasking === null || tasking === void 0 ? void 0 : tasking.fuelLog) ? (_tasking_fuelLog_find = tasking.fuelLog.find((log)=>+log.fuelTank.id === +tank.id)) === null || _tasking_fuelLog_find === void 0 ? void 0 : _tasking_fuelLog_find.fuelAfter : getInitialFuelLevel(tank)) !== null && _ref !== void 0 ? _ref : 0,\n                                            min: 0,\n                                            max: tank.capacity,\n                                            onChange: (e)=>handleUpdateFuelTank(tank, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1732,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, tank.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 1725,\n                                    columnNumber: 29\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2 my-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                children: \"Location where tasking takes place\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1759,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                offline: offline,\n                                                setCurrentLocation: setCurrentLocation,\n                                                handleLocationChange: handleLocationChange,\n                                                currentEvent: tripEvent.eventType_Tasking\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1760,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1758,\n                                        columnNumber: 25\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                            id: \"location-description\",\n                                            rows: 4,\n                                            placeholder: \"Location description\",\n                                            value: locationDescription !== null && locationDescription !== void 0 ? locationDescription : \"\",\n                                            onChange: (e)=>{\n                                                setLocationDescription(e.target.value);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1771,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1770,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1757,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    type === \"TaskingPaused\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                                    options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Open\").map((group)=>({\n                                                            value: group.eventType_Tasking.id,\n                                                            label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                        })) : [],\n                                                    value: tasking.pausedTaskID > 0 ? {\n                                                        value: tasking.pausedTaskID,\n                                                        label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.title)\n                                                    } : taskingPausedValue,\n                                                    onChange: handleTaskingPauseChange,\n                                                    placeholder: \"Select Task to pause\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1789,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1788,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: (selectedEvent && content || !selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    id: \"comment\",\n                                                    placeholder: \"Comment\",\n                                                    className: \"w-full\",\n                                                    content: content,\n                                                    handleEditorChange: handleEditorChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1837,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1834,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status !== \"Completed\").map((group)=>({\n                                                    value: group.eventType_Tasking.id,\n                                                    label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                })) : [],\n                                            value: tasking.completedTaskID > 0 ? {\n                                                value: tasking.completedTaskID,\n                                                label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find2 = _currentTrip_tripEvents2.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find2 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find2.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find3 = _currentTrip_tripEvents3.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find3 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find3.eventType_Tasking.title)\n                                            } : getPreviousTask(taskingCompleteValue),\n                                            onChange: handleTaskingCompleteChange,\n                                            placeholder: \"Select Task to Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1852,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1851,\n                                        columnNumber: 29\n                                    }, this),\n                                    type === \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                        options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Paused\").map((group)=>({\n                                                value: group.eventType_Tasking.id,\n                                                label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                            })) : [],\n                                        value: tasking.openTaskID > 0 ? {\n                                            value: tasking.openTaskID,\n                                            label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents4 = currentTrip.tripEvents) === null || _currentTrip_tripEvents4 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find4 = _currentTrip_tripEvents4.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find4 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find4.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents5 = currentTrip.tripEvents) === null || _currentTrip_tripEvents5 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find5 = _currentTrip_tripEvents5.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find5 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find5.eventType_Tasking.title)\n                                        } : taskingResumedValue,\n                                        onChange: handleTaskingGroupChange,\n                                        placeholder: \"Select Task to continue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1898,\n                                        columnNumber: 29\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && type !== \"TaskingComplete\" && type !== \"TaskingOnTow\" && type !== \"TaskingOnScene\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: operationTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: operationTypes.map((type)=>({\n                                                    value: type.value,\n                                                    label: type.label\n                                                })),\n                                            value: {\n                                                value: currentOperationTypeValue(tasking === null || tasking === void 0 ? void 0 : tasking.operationType),\n                                                label: currentOperationTypeLabel(tasking === null || tasking === void 0 ? void 0 : tasking.operationType)\n                                            },\n                                            onChange: handleOperationTypeChange,\n                                            placeholder: \"Operation type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1945,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1785,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                                className: \"max-w-[40rem]\",\n                                children: [\n                                    \"Everything else below this section is\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"optional can be completed later\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1969,\n                                        columnNumber: 25\n                                    }, this),\n                                    \". However, all the details loaded here will be used for any tasking reports required.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1967,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1693,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                locationDescription: locationDescription,\n                setLocationDescription: setLocationDescription,\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousRescueID(tasking.vesselRescueID),\n                type: type,\n                eventCurrentLocation: {\n                    currentLocation: currentLocation,\n                    geoLocationID: tasking.geoLocationID\n                },\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 1978,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayPersonRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_person_rescue_field__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousHumanRescueID(tasking.personRescueID),\n                type: type,\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2002,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2.5 bg-accent rounded-md\",\n                children: [\n                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-4 text-sm font-semibold uppercase\",\n                                children: \"Incident type / number\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2020,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                                className: \"max-w-[40rem]\",\n                                children: \"Detail if incident was tasked by Police, RCCNZ or Coastguard and associated incident number if applicable\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2023,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex my-6 w-full flex-wrap items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                htmlFor: \"task-cgop\",\n                                                label: \"CoastGuard Rescue\",\n                                                leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                                    id: \"task-cgop\",\n                                                    isRadioStyle: true,\n                                                    size: \"lg\",\n                                                    checked: getIsCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop),\n                                                    onCheckedChange: (checked)=>{\n                                                        if (checked) handleCgopChange({\n                                                            target: {\n                                                                value: \"on\"\n                                                            }\n                                                        });\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 2034,\n                                                    columnNumber: 41\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 2030,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                                    id: \"cgop\",\n                                                    type: \"text\",\n                                                    onChange: (e)=>{\n                                                        setTasking({\n                                                            ...tasking,\n                                                            sarop: \"\",\n                                                            cgop: e.target.value\n                                                        }), setCurrentIncident(\"cgop\");\n                                                    },\n                                                    value: (_getPreviousCGOP = getPreviousCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop)) !== null && _getPreviousCGOP !== void 0 ? _getPreviousCGOP : \"\",\n                                                    \"aria-describedby\": \"cgop-error\",\n                                                    required: true,\n                                                    placeholder: \"Police / RCCNZ number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 2051,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 2049,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2029,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                htmlFor: \"task-sarop\",\n                                                label: \"SAROP\",\n                                                leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                                    id: \"task-sarop\",\n                                                    isRadioStyle: true,\n                                                    size: \"lg\",\n                                                    checked: getIsSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop),\n                                                    onCheckedChange: (checked)=>{\n                                                        if (checked) handleSaropChange({\n                                                            target: {\n                                                                value: \"on\"\n                                                            }\n                                                        });\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 2076,\n                                                    columnNumber: 41\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 2072,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                                    id: \"sarop\",\n                                                    type: \"text\",\n                                                    onChange: (e)=>{\n                                                        setTasking({\n                                                            ...tasking,\n                                                            sarop: e.target.value,\n                                                            cgop: \"\"\n                                                        }), setCurrentIncident(\"sarop\");\n                                                    },\n                                                    value: (_getPreviousSAROP = getPreviousSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop)) !== null && _getPreviousSAROP !== void 0 ? _getPreviousSAROP : \"\",\n                                                    \"aria-describedby\": \"sarop-error\",\n                                                    required: true,\n                                                    placeholder: \"Police / RCCNZ number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 2093,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 2091,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2071,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2028,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_31__.Button, {\n                                variant: \"back\",\n                                onClick: ()=>closeModal(),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2119,\n                                columnNumber: 21\n                            }, this),\n                            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_31__.Button, {\n                                onClick: ()=>handleSave(0, 0),\n                                children: selectedEvent ? \"Update\" : \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2123,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2118,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2017,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2137,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2136,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                            id: \"parent-location\",\n                            options: locations || [],\n                            onChange: handleParentLocationChange,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2146,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2145,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2155,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2154,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2165,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2164,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2130,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_risk_analysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                offline: offline,\n                selectedEvent: selectedEvent,\n                crewMembers: members,\n                towingChecklistID: towingChecklistID,\n                setTowingChecklistID: setTowingChecklistID,\n                setAllChecked: setAllChecked,\n                onSidebarClose: ()=>setOpenRiskAnalysis(false),\n                logBookConfig: undefined,\n                currentTrip: currentTrip,\n                open: openRiskAnalysis,\n                onOpenChange: setOpenRiskAnalysis\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2176,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n        lineNumber: 1650,\n        columnNumber: 9\n    }, this);\n}\n_s(Tasking, \"GFJkr1zSemheuoxajglNxR7bBSE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useLazyQuery\n    ];\n});\n_c = Tasking;\nvar _c;\n$RefreshReg$(_c, \"Tasking\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\n"));

/***/ })

});
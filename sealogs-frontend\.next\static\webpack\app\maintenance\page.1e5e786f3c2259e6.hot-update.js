"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    setImage(url);\n                    setDisplayImage(true);\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    setImage(base64Image);\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    setImage(textContent);\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                    setDisplayImage(true);\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n            return;\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                description: \"No camera found. Please connect a camera.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            setImage(imageData);\n            setDisplayImage(true);\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                description: \"No other camera found to switch.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                description: \"Please capture or upload an image first.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n            description: \"Please capture or upload an image first.\",\n            variant: \"destructive\"\n        });\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n            description: \"Images uploaded successfully.\"\n        });\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                    description: \"Image deleted successfully.\"\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                description: \"Failed to delete image.\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                        description: \"Image deleted successfully.\"\n                    });\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-neutral-400 group-hover:text-neutral-400/50\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 475,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    if (image) {\n                        handleUploadFile();\n                    // setOpenCameraDialog(false)\n                    } else {\n                        (0,sonner__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                            description: \"Please capture an image first.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2 justify-between\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setImage(null);\n                                        setDisplayImage(false);\n                                        handleOpenCameraDialog(true);\n                                    },\n                                    className: \"mt-2\",\n                                    children: \"Recapture\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false),\n                            devices.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                className: \"mt-2\",\n                                children: \"Switch Camera\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2\",\n                                children: \"Capture Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2\",\n                                children: \"Upload Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 505,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"tO+FzjAwrNpn7Czrp7cxVnPeGmE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});
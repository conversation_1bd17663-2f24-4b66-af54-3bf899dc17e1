"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/logbook/forms/tasking.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Tasking; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./vessel-rescue-fields */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\");\n/* harmony import */ var _person_rescue_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./person-rescue-field */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue-field.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _risk_analysis__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _barrel_optimize_names_SquareArrowOutUpRight_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=SquareArrowOutUpRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square-arrow-out-up-right.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons/SealogsFuelIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsFuelIcon.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/fuelTank */ \"(app-pages-browser)/./src/app/offline/models/fuelTank.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/fuelLog */ \"(app-pages-browser)/./src/app/offline/models/fuelLog.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Combobox is already imported from '@/components/ui/comboBox'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Tasking(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, inLogbook = false, previousDropEvent, vessel, members, locked, offline = false, fuelLogs } = param;\n    var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents, _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1, _currentTrip_tripEvents_nodes_find2, _currentTrip_tripEvents2, _currentTrip_tripEvents_nodes_find3, _currentTrip_tripEvents3, _currentTrip_tripEvents_nodes_find4, _currentTrip_tripEvents4, _currentTrip_tripEvents_nodes_find5, _currentTrip_tripEvents5;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\"));\n    const [tasking, setTasking] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openRiskAnalysis, setOpenRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [pauseGroup, setPauseGroup] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openTaskID, setOpenTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [completedTaskID, setCompletedTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [towingChecklistID, setTowingChecklistID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [groupID, setGroupID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentIncident, setCurrentIncident] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [taskingPausedValue, setTaskingPausedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingResumedValue, setTaskingResumedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingCompleteValue, setTaskingCompleteValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [locationDescription, setLocationDescription] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // const [members, setMembers] = useState<any>(false)\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankList, setFuelTankList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankBuffer, setFuelTankBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [updatedFuelLogs, setUpdatedFuelLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const fuelTankModel = new _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__[\"default\"]();\n    const fuelLogModel = new _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryGetFuelTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELTANKS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readFuelTanks.nodes;\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelTanks error\", error);\n        }\n    });\n    const getFuelTanks = async (fuelTankIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelTankIds);\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        } else {\n            await queryGetFuelTanks({\n                variables: {\n                    id: fuelTankIds\n                }\n            });\n        }\n    };\n    const handleSetVessel = (vessel)=>{\n        var _vessel_parentComponent_Components;\n        const fuelTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components === void 0 ? void 0 : _vessel_parentComponent_Components.nodes.filter((item)=>item.basicComponent.componentCategory === \"FuelTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (vessel) {\n            handleSetVessel(vessel);\n        }\n    }, [\n        vessel\n    ]);\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"HH:mm\"));\n    };\n    const offlineGetPreviousDropEvent = async ()=>{\n        const event = await tripEventModel.getById(previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id);\n        if (event) {\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n            if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                setCurrentLocation({\n                    latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                    longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        } else {\n            if ((previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id) > 0) {\n                if (offline) {\n                    offlineGetPreviousDropEvent();\n                } else {\n                    getPreviousDropEvent({\n                        variables: {\n                            id: previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const handleTaskingPauseChange = (selectedTask)=>{\n        setPauseGroup(selectedTask.value);\n        setTaskingPausedValue(selectedTask);\n    };\n    const [getPreviousDropEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n                setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n                if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                    var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                        longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                    });\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting previous event\", error);\n        }\n    });\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            var _currentTrip_tripEvents;\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                // eventType_TaskingID\n                if (!event.eventType_Tasking) {\n                    const eventType_Tasking = await taskingModel.getById(event.eventType_TaskingID);\n                    event.eventType_Tasking = eventType_Tasking;\n                }\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _currentTrip_tripEvents;\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations([\n                {\n                    label: \"--- Add new location ---\",\n                    value: \"newLocation\"\n                },\n                ...geoLocations.filter((location)=>location.title).map((location)=>({\n                        label: location.title,\n                        value: location.id,\n                        latitude: location.lat,\n                        longitude: location.long\n                    }))\n            ]);\n        }\n    }, [\n        geoLocations\n    ]);\n    const handleSave = async function() {\n        let vesselRescueID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, personRescueID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const variables = {\n            input: {\n                geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                time: time,\n                title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                type: type,\n                operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                vesselRescueID: vesselRescueID > 0 ? vesselRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.vesselRescueID,\n                personRescueID: personRescueID > 0 ? personRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.personRescueID,\n                currentEntryID: currentTrip.id,\n                tripEventID: tasking === null || tasking === void 0 ? void 0 : tasking.id,\n                pausedTaskID: +pauseGroup,\n                openTaskID: +openTaskID,\n                completedTaskID: +completedTaskID,\n                comments: content,\n                groupID: +groupID,\n                status: \"Open\",\n                cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : null,\n                sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : null\n            }\n        };\n        if (pauseGroup > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +pauseGroup,\n                    status: \"Paused\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +pauseGroup,\n                            status: \"Paused\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (openTaskID > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +openTaskID,\n                    status: \"Open\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +openTaskID,\n                            status: \"Open\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (completedTaskID > 0 && !currentEvent) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +completedTaskID > 0 ? +completedTaskID : (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    status: \"Completed\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +completedTaskID,\n                            status: \"Completed\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (currentEvent) {\n            if (offline) {\n                const data = await taskingModel.save({\n                    ...variables.input,\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                    tripEventID: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id)\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n                await getCurrentEvent(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                            ...variables.input\n                        }\n                    }\n                });\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            }\n        } else {\n            if (offline) {\n                const newID = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)();\n                await tripEventModel.save({\n                    id: newID,\n                    eventCategory: \"Tasking\",\n                    eventType_TaskingID: +newID,\n                    logBookEntrySectionID: currentTrip.id\n                });\n                const data = await taskingModel.save({\n                    id: +newID,\n                    geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                    time: time,\n                    title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                    fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                    type: type,\n                    operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                    lat: currentLocation.latitude.toString(),\n                    long: currentLocation.longitude.toString(),\n                    vesselRescueID: vesselRescueID,\n                    personRescueID: personRescueID,\n                    currentEntryID: currentTrip.id,\n                    pausedTaskID: +pauseGroup,\n                    openTaskID: +openTaskID,\n                    completedTaskID: +completedTaskID,\n                    comments: content,\n                    groupID: +groupID,\n                    status: \"Open\",\n                    cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                    sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false),\n                    tripEventID: newID\n                });\n                updateFuelLogs(+data.id);\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n            } else {\n                createEventType_Tasking({\n                    variables: {\n                        input: {\n                            geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                            time: time,\n                            title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                            fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                            type: type,\n                            operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                            lat: currentLocation.latitude.toString(),\n                            long: currentLocation.longitude.toString(),\n                            vesselRescueID: vesselRescueID,\n                            personRescueID: personRescueID,\n                            currentEntryID: currentTrip.id,\n                            pausedTaskID: +pauseGroup,\n                            openTaskID: +openTaskID,\n                            completedTaskID: +completedTaskID,\n                            comments: content,\n                            groupID: +groupID,\n                            status: \"Open\",\n                            cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                            sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false)\n                        }\n                    }\n                });\n            }\n        }\n        setCompletedTaskID(false);\n        setOpenTaskID(false);\n        setPauseGroup(false);\n    };\n    const [createEventType_Tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CreateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_Tasking;\n            updateFuelLogs(+data.id);\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.message);\n        }\n    });\n    const [updateEventType_tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_tasking;\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating activity type tasking\", error);\n        }\n    });\n    const handleOperationTypeChange = (selectedOperation)=>{\n        if (selectedOperation.value === \"newLocation\") {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.loading(\"Getting your current location... Please wait...\");\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Location found\");\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Geolocation is not supported by your browser\");\n                setOpenNewLocationDialog(true);\n            }\n        } else {\n            setTasking({\n                ...tasking,\n                operationType: selectedOperation.value\n            });\n        }\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            // Update tripEvent to clear location data\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    geoLocationID: 0,\n                    lat: null,\n                    long: null\n                }\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.loading(\"Getting your current location... Please wait...\");\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Geolocation is not supported by your browser\");\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTasking({\n                ...tasking,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n            // Update tripEvent to reflect the selected location\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    geoLocationID: +value.value,\n                    lat: null,\n                    long: null\n                }\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n            // Update tripEvent to reflect the new coordinates so LocationField can display them\n            setTripEvent({\n                ...tripEvent,\n                eventType_Tasking: {\n                    ...tripEvent === null || tripEvent === void 0 ? void 0 : tripEvent.eventType_Tasking,\n                    lat: value.latitude,\n                    long: value.longitude,\n                    geoLocationID: 0\n                }\n            });\n        }\n    };\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTasking({\n                    ...tasking,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTasking({\n                ...tasking,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Error creating GeoLocation\");\n            console.error(\"Error creating GeoLocation: \" + error.message);\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    const handleParentLocationChange = (selectedOption)=>{\n        if (selectedOption) {\n            setParentLocation(selectedOption.value);\n        } else {\n            setParentLocation(null);\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleTaskingGroupChange = (selectedGroup)=>{\n        setGroupID(selectedGroup.value);\n        setOpenTaskID(selectedGroup.value);\n        setTaskingResumedValue(selectedGroup);\n    };\n    const handleTaskingCompleteChange = (selectedGroup)=>{\n        setCompletedTaskID(selectedGroup.value);\n        setTaskingCompleteValue(selectedGroup);\n    };\n    const operationTypes = [\n        {\n            label: \"Vessel Mechanical / equipment failure\",\n            value: \"Vessel Mechanical or equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Vessel - other\",\n            value: \"Vessel - other\"\n        },\n        {\n            label: \"Person in water\",\n            value: \"Person in water\"\n        },\n        {\n            label: \"Person lost / missing\",\n            value: \"Person lost or missing\"\n        },\n        {\n            label: \"Suicide\",\n            value: \"Suicide\"\n        },\n        {\n            label: \"Medical condition\",\n            value: \"Medical condition\"\n        },\n        {\n            label: \"Person - other\",\n            value: \"Person - other\"\n        }\n    ];\n    const goSetTaskingTitle = (event)=>{\n        let title = \"\";\n        if (event && event.eventType_Tasking.type === \"TaskingStartUnderway\") {\n            title = event.eventType_Tasking.title;\n        }\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(tasking.title)) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(title))) {\n            setTasking({\n                ...tasking,\n                title: title\n            });\n        }\n    };\n    const findPreviousEvent = (selectedEvent)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            if (selectedEvent) {\n                if (selectedEvent.eventType_Tasking.vesselRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.vesselRescueID === selectedEvent.eventType_Tasking.vesselRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n                if (selectedEvent.eventType_Tasking.personRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.personRescueID === selectedEvent.eventType_Tasking.personRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n            }\n            goSetTaskingTitle(prevEvent);\n            return prevEvent;\n        }\n        if (type === \"TaskingComplete\") {\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents1;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            } else {\n                const res = prevEvent ? prevEvent : selectedEvent;\n                goSetTaskingTitle(res);\n                return res;\n            }\n        }\n        goSetTaskingTitle(selectedEvent);\n        return selectedEvent;\n    };\n    const findPreviousRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.vesselRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const findPreviousHumanRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.personRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const currentOperationTypeLabel = (label)=>{\n        return label ? label : \"-- Select operation type --\";\n    };\n    const currentOperationTypeValue = (value)=>{\n        return value;\n    };\n    const getPreviousSAROP = (sarop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"cgop\") {\n            return \"\";\n        }\n        if (currentIncident === \"sarop\") {\n            return sarop ? sarop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.sarop) ? e.eventType_Tasking.sarop : sarop ? sarop : \"\";\n    };\n    const getPreviousCGOP = (cgop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"sarop\") {\n            return \"\";\n        }\n        if (currentIncident === \"cgop\") {\n            return cgop ? cgop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.cgop) ? e.eventType_Tasking.cgop : cgop ? cgop : \"\";\n    };\n    const getIsSAROP = (sarop)=>{\n        var _tasking_cgop, _tasking_sarop;\n        return currentIncident === \"sarop\" || currentIncident !== \"sarop\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(sarop)) && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_cgop = tasking === null || tasking === void 0 ? void 0 : tasking.cgop) !== null && _tasking_cgop !== void 0 ? _tasking_cgop : \"\")) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_sarop = tasking === null || tasking === void 0 ? void 0 : tasking.sarop) !== null && _tasking_sarop !== void 0 ? _tasking_sarop : \"\"));\n    };\n    const getIsCGOP = (cgop)=>{\n        var _tasking_sarop, _tasking_cgop;\n        return currentIncident === \"cgop\" || currentIncident !== \"cgop\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(cgop)) && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_sarop = tasking === null || tasking === void 0 ? void 0 : tasking.sarop) !== null && _tasking_sarop !== void 0 ? _tasking_sarop : \"\")) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()((_tasking_cgop = tasking === null || tasking === void 0 ? void 0 : tasking.cgop) !== null && _tasking_cgop !== void 0 ? _tasking_cgop : \"\"));\n    };\n    const getPreviousFuelLevel = (fuelLevel)=>{\n        var _selectedEvent_eventType_Tasking, _currentTrip_tripEvents;\n        if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.fuelLevel) > 0) {\n            var _selectedEvent_eventType_Tasking1;\n            return selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking1.fuelLevel;\n        }\n        if (fuelLevel || (tasking === null || tasking === void 0 ? void 0 : tasking.updatedFuelLevel)) {\n            return fuelLevel;\n        }\n        const fuelLevels = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>event.eventType_Tasking.fuelLevel > 0).map((event)=>event.eventType_Tasking.fuelLevel);\n        const minFuelLevel = (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? fuelLevels[fuelLevels.length - 1] : fuelLevel;\n        return (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? minFuelLevel : fuelLevel ? fuelLevel : \"\";\n    };\n    const getPreviousTask = (task)=>{\n        var _prevEvent_eventType_Tasking, _prevEvent_eventType_Tasking1, _prevEvent_eventType_Tasking2;\n        if (task) {\n            return task;\n        }\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _prevEvent_eventType_Tasking3, _prevEvent_eventType_Tasking4, _prevEvent_eventType_Tasking5, _prevEvent_eventType_Tasking6;\n            setCompletedTaskID(prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking3 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking3 === void 0 ? void 0 : _prevEvent_eventType_Tasking3.id);\n            setTaskingCompleteValue({\n                label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking4 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking4 === void 0 ? void 0 : _prevEvent_eventType_Tasking4.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking5 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking5 === void 0 ? void 0 : _prevEvent_eventType_Tasking5.title),\n                value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking6 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking6 === void 0 ? void 0 : _prevEvent_eventType_Tasking6.id\n            });\n        }\n        return prevEvent ? {\n            label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking === void 0 ? void 0 : _prevEvent_eventType_Tasking.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking1 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking1 === void 0 ? void 0 : _prevEvent_eventType_Tasking1.title),\n            value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking2 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking2 === void 0 ? void 0 : _prevEvent_eventType_Tasking2.id\n        } : task;\n    };\n    const isVesselRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.vesselRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID) > 0;\n    };\n    const isPersonRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.personRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID) > 0;\n    };\n    const displayVessesRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isVesselRescue() || type === \"TaskingOnTow\" && isVesselRescue() || type === \"TaskingComplete\" && isVesselRescue() || tasking.operationType === \"Vessel Mechanical or equipment failure\" || tasking.operationType === \"Vessel adrift\" || tasking.operationType === \"Vessel aground\" || tasking.operationType === \"Capsize\" || tasking.operationType === \"Vessel requiring tow\" || tasking.operationType === \"Flare sighting\" || tasking.operationType === \"Vessel sinking\" || tasking.operationType === \"Collision\" || tasking.operationType === \"Vessel overdue\" || tasking.operationType === \"Vessel - other\" || +tasking.lat > 0 && +tasking.long > 0) {\n            return true;\n        }\n        return false;\n    };\n    const displayPersonRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isPersonRescue() || type === \"TaskingOnTow\" && isPersonRescue() || type === \"TaskingComplete\" && isPersonRescue() || tasking.operationType === \"Person in water\" || tasking.operationType === \"Person lost or missing\" || tasking.operationType === \"Suicide\" || tasking.operationType === \"Medical condition\" || tasking.operationType === \"Person - other\") {\n            return true;\n        }\n        return false;\n    };\n    const handleSaropChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"sarop\");\n        }\n    };\n    const handleCgopChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"cgop\");\n        }\n    };\n    const handleUpdateFuelTank = (tank, value)=>{\n        if (tank.capacity < +value) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Fuel level cannot be higher than tank capacity of \" + tank.capacity);\n            return;\n        }\n        setFuelTankList(fuelTankList.map((item)=>{\n            if (item.id === tank.id) {\n                return {\n                    ...item,\n                    currentLevel: +value\n                };\n            }\n            return item;\n        }));\n        setTasking({\n            ...tasking,\n            fuelLog: false\n        });\n        if (fuelTankBuffer.length > 0 && fuelTankBuffer.filter((item)=>item.tankID === tank.id)) {\n            setFuelTankBuffer(fuelTankBuffer.map((item)=>{\n                if (item.tankID === tank.id) {\n                    return {\n                        ...item,\n                        value: +value\n                    };\n                }\n                return item;\n            }));\n        } else {\n            setFuelTankBuffer([\n                ...fuelTankBuffer,\n                {\n                    tankID: tank.id,\n                    value: +value\n                }\n            ]);\n        }\n    };\n    // Create a debounced version of the update function\n    // const handleUpdateFuelTank = useCallback(\n    //     debounce((tank: any, value: any) => {\n    //         updateFuelTankValue(tank, value)\n    //     }, 500), // 500ms delay\n    //     [fuelTankList, tasking],\n    // )\n    const [updateFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UPDATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel log\", error);\n        }\n    });\n    const [createFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.createFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error creating fuel log\", error);\n        }\n    });\n    const [updateFuelTank] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateFuelTank, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelTank;\n            const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === data.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n            if (fuelLog) {\n                updateFuelLog({\n                    variables: {\n                        input: {\n                            id: fuelLog.id,\n                            fuelAfter: +fuelLog.fuelAfter\n                        }\n                    }\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel tank\", error);\n        }\n    });\n    const updateFuelLogs = function() {\n        let currentID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        if (fuelTankList) {\n            Promise.all(fuelTankList === null || fuelTankList === void 0 ? void 0 : fuelTankList.map(async (fuelTank)=>{\n                const variables = {\n                    input: {\n                        id: fuelTank.id,\n                        currentLevel: fuelTank.currentLevel\n                    }\n                };\n                if (!currentEvent) {\n                    if (offline) {\n                        await fuelTankModel.save({\n                            id: fuelTank.id,\n                            currentLevel: fuelTank.currentLevel\n                        });\n                    } else {\n                        updateFuelTank({\n                            variables: variables\n                        });\n                    }\n                }\n                if (currentEvent) {\n                    if (offline) {\n                        var _currentEvent_eventType_Tasking_fuelLog_nodes_find;\n                        await fuelLogModel.save({\n                            id: ((_currentEvent_eventType_Tasking_fuelLog_nodes_find = currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>{\n                                var _log_fuelTank;\n                                ((_log_fuelTank = log.fuelTank) === null || _log_fuelTank === void 0 ? void 0 : _log_fuelTank.id) === fuelTank.id;\n                            })) === null || _currentEvent_eventType_Tasking_fuelLog_nodes_find === void 0 ? void 0 : _currentEvent_eventType_Tasking_fuelLog_nodes_find.id) || (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        updateFuelLog({\n                            variables: {\n                                input: {\n                                    id: currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>log.fuelTank.id === fuelTank.id).id,\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                } else {\n                    if (offline) {\n                        await fuelLogModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        createFuelLog({\n                            variables: {\n                                input: {\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                }\n            }));\n        }\n    };\n    const getInitialFuelLevel = (tank)=>{\n        if (fuelTankBuffer.length > 0) {\n            const fuelTank = fuelTankBuffer.find((item)=>item.tankID === tank.id);\n            if (fuelTank) {\n                return fuelTank.value;\n            }\n        }\n        if (tripReport.length > 0) {\n            var _fuelLogs_filter_sort;\n            const fuelLogs = tripReport.map((trip)=>{\n                return trip.tripEvents.nodes.filter((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes.length > 0 || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes.length > 0 || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes.length > 0).flatMap((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes);\n            }).flat();\n            const lastFuelLog = fuelLogs === null || fuelLogs === void 0 ? void 0 : (_fuelLogs_filter_sort = fuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>b.id - a.id)) === null || _fuelLogs_filter_sort === void 0 ? void 0 : _fuelLogs_filter_sort[0];\n            if (lastFuelLog) {\n                return lastFuelLog.fuelAfter;\n            }\n        }\n        // if (\n        //     currentTrip &&\n        //     currentTrip?.tripEvents?.nodes?.length > 0 &&\n        //     currentTrip.tripEvents.nodes.find(\n        //         (event: any) =>\n        //             event.eventCategory === 'Tasking' &&\n        //             event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //     )\n        // ) {\n        //     const fuelLog = currentTrip.tripEvents.nodes\n        //         .filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (fuelLog) {\n        //         return fuelLog.fuelAfter\n        //     }\n        // }\n        // if (tripReport && tripReport.length > 1) {\n        //     const latestTripFuelLog = tripReport\n        //         .filter((trip: any) => trip.id < currentTrip.id)\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.tripEvents?.nodes.filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (latestTripFuelLog) {\n        //         return latestTripFuelLog.fuelAfter\n        //     }\n        // }\n        const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n        return fuelLog ? +tank.capacity > +fuelLog.fuelAfter ? +fuelLog.fuelAfter : +tank.capacity : +tank.currentLevel;\n    };\n    const getFuelLogs = async (fuelLogIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelLogIds);\n        } else {\n            await queryGetFuelLogs({\n                variables: {\n                    id: fuelLogIds\n                }\n            });\n        }\n    };\n    const [queryGetFuelLogs] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELLOGS, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.readFuelLogs.nodes;\n            setUpdatedFuelLogs(data);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelLogs error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFuelLogs(fuelLogs.map((item)=>item.id));\n    }, []);\n    var _getPreviousCGOP, _getPreviousSAROP;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !inLogbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 text-sm font-semibold uppercase\",\n                        children: [\n                            type === \"TaskingStartUnderway\" && \"Tasking start / underway\",\n                            type === \"TaskingPaused\" && \"Tasking paused\",\n                            type === \"TaskingResumed\" && \"Tasking resumed\",\n                            type === \"TaskingOnScene\" && tasking.title,\n                            type === \"TaskingOnTow\" && tasking.title,\n                            type === \"TaskingComplete\" && tasking.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1653,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem] mb-2\",\n                        children: [\n                            \"Give this tasking a title and choose an operation type.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1665,\n                                columnNumber: 21\n                            }, this),\n                            \"Recording fuel levels goes toward\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"fuel reports for allocating to different operations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1667,\n                                columnNumber: 21\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1663,\n                        columnNumber: 17\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                        className: \"max-w-[40rem]\",\n                        children: \"Utilise attached checklist to ensure towing procedure is followed and any risks identified.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1673,\n                        columnNumber: 21\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_29__.CheckFieldLabel, {\n                        type: \"checkbox\",\n                        checked: allChecked,\n                        className: \"w-fit\",\n                        variant: \"success\",\n                        rightContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SquareArrowOutUpRight_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 1685,\n                            columnNumber: 29\n                        }, void 0),\n                        onClick: ()=>{\n                            setOpenRiskAnalysis(true);\n                        },\n                        label: \"Towing checklist - risk analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1679,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Time when tasking takes place\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1696,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        time: time,\n                                        handleTimeChange: handleTimeChange,\n                                        timeID: \"time\",\n                                        fieldName: \"Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1697,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1694,\n                                columnNumber: 21\n                            }, this),\n                            type === \"TaskingStartUnderway\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Title of tasking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1707,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                        id: \"title\",\n                                        name: \"title\",\n                                        type: \"text\",\n                                        value: (tasking === null || tasking === void 0 ? void 0 : tasking.title) ? tasking.title : \"\",\n                                        placeholder: \"Title\",\n                                        onChange: (e)=>{\n                                            setTasking({\n                                                ...tasking,\n                                                title: e.target.value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1708,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1705,\n                                columnNumber: 25\n                            }, this),\n                            fuelTankList && fuelTankList.map((tank)=>/*#__PURE__*/ {\n                                var _tasking_fuelLog_find;\n                                var _tank_currentLevel, _ref;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" flex flex-col gap-2 my-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__.SealogsFuelIcon, {\n                                                    className: \"size-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1729,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                    children: tank.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1730,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1728,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                            type: \"number\",\n                                            placeholder: \"Fuel end\",\n                                            value: (_ref = (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : (tasking === null || tasking === void 0 ? void 0 : tasking.fuelLog) ? (_tasking_fuelLog_find = tasking.fuelLog.find((log)=>+log.fuelTank.id === +tank.id)) === null || _tasking_fuelLog_find === void 0 ? void 0 : _tasking_fuelLog_find.fuelAfter : getInitialFuelLevel(tank)) !== null && _ref !== void 0 ? _ref : 0,\n                                            min: 0,\n                                            max: tank.capacity,\n                                            onChange: (e)=>handleUpdateFuelTank(tank, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1732,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, tank.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 1725,\n                                    columnNumber: 29\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2 my-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                children: \"Location where tasking takes place\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1759,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                offline: offline,\n                                                setCurrentLocation: setCurrentLocation,\n                                                handleLocationChange: handleLocationChange,\n                                                currentEvent: tripEvent.eventType_Tasking\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1760,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1758,\n                                        columnNumber: 25\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                            id: \"location-description\",\n                                            rows: 4,\n                                            placeholder: \"Location description\",\n                                            value: locationDescription !== null && locationDescription !== void 0 ? locationDescription : \"\",\n                                            onChange: (e)=>{\n                                                setLocationDescription(e.target.value);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1771,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1770,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1757,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    type === \"TaskingPaused\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                                    options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Open\").map((group)=>({\n                                                            value: group.eventType_Tasking.id,\n                                                            label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                        })) : [],\n                                                    value: tasking.pausedTaskID > 0 ? {\n                                                        value: tasking.pausedTaskID,\n                                                        label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.title)\n                                                    } : taskingPausedValue,\n                                                    onChange: handleTaskingPauseChange,\n                                                    placeholder: \"Select Task to pause\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1789,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1788,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: (selectedEvent && content || !selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    id: \"comment\",\n                                                    placeholder: \"Comment\",\n                                                    className: \"w-full\",\n                                                    content: content,\n                                                    handleEditorChange: handleEditorChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1837,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1834,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status !== \"Completed\").map((group)=>({\n                                                    value: group.eventType_Tasking.id,\n                                                    label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                })) : [],\n                                            value: tasking.completedTaskID > 0 ? {\n                                                value: tasking.completedTaskID,\n                                                label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find2 = _currentTrip_tripEvents2.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find2 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find2.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find3 = _currentTrip_tripEvents3.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find3 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find3.eventType_Tasking.title)\n                                            } : getPreviousTask(taskingCompleteValue),\n                                            onChange: handleTaskingCompleteChange,\n                                            placeholder: \"Select Task to Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1852,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1851,\n                                        columnNumber: 29\n                                    }, this),\n                                    type === \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                        options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Paused\").map((group)=>({\n                                                value: group.eventType_Tasking.id,\n                                                label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                            })) : [],\n                                        value: tasking.openTaskID > 0 ? {\n                                            value: tasking.openTaskID,\n                                            label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents4 = currentTrip.tripEvents) === null || _currentTrip_tripEvents4 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find4 = _currentTrip_tripEvents4.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find4 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find4.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents5 = currentTrip.tripEvents) === null || _currentTrip_tripEvents5 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find5 = _currentTrip_tripEvents5.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find5 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find5.eventType_Tasking.title)\n                                        } : taskingResumedValue,\n                                        onChange: handleTaskingGroupChange,\n                                        placeholder: \"Select Task to continue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1898,\n                                        columnNumber: 29\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && type !== \"TaskingComplete\" && type !== \"TaskingOnTow\" && type !== \"TaskingOnScene\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: operationTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: operationTypes.map((type)=>({\n                                                    value: type.value,\n                                                    label: type.label\n                                                })),\n                                            value: {\n                                                value: currentOperationTypeValue(tasking === null || tasking === void 0 ? void 0 : tasking.operationType),\n                                                label: currentOperationTypeLabel(tasking === null || tasking === void 0 ? void 0 : tasking.operationType)\n                                            },\n                                            onChange: handleOperationTypeChange,\n                                            placeholder: \"Operation type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1945,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1785,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                                className: \"max-w-[40rem]\",\n                                children: [\n                                    \"Everything else below this section is\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"optional can be completed later\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1969,\n                                        columnNumber: 25\n                                    }, this),\n                                    \". However, all the details loaded here will be used for any tasking reports required.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1967,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1693,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                locationDescription: locationDescription,\n                setLocationDescription: setLocationDescription,\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousRescueID(tasking.vesselRescueID),\n                type: type,\n                eventCurrentLocation: {\n                    currentLocation: currentLocation,\n                    geoLocationID: tasking.geoLocationID\n                },\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 1978,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayPersonRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_person_rescue_field__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousHumanRescueID(tasking.personRescueID),\n                type: type,\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2002,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2.5 bg-accent rounded-md\",\n                children: [\n                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_32__.Accordion, {\n                        type: \"single\",\n                        collapsible: true,\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_32__.AccordionItem, {\n                            value: \"incident-number\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_32__.AccordionTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold uppercase\",\n                                        children: \"Incident Number\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2022,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 2021,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_32__.AccordionContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_30__.P, {\n                                            className: \"max-w-[40rem] mb-4\",\n                                            children: \"Detail if incident was tasked by Police, RCCNZ or Coastguard and associated incident number if applicable\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 2027,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex w-full flex-wrap items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                            htmlFor: \"task-cgop\",\n                                                            label: \"CoastGuard Rescue\",\n                                                            leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                                                id: \"task-cgop\",\n                                                                isRadioStyle: true,\n                                                                size: \"lg\",\n                                                                checked: getIsCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop),\n                                                                onCheckedChange: (checked)=>{\n                                                                    if (checked) handleCgopChange({\n                                                                        target: {\n                                                                            value: \"on\"\n                                                                        }\n                                                                    });\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                                lineNumber: 2038,\n                                                                columnNumber: 49\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                            lineNumber: 2034,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                                                id: \"cgop\",\n                                                                type: \"text\",\n                                                                onChange: (e)=>{\n                                                                    setTasking({\n                                                                        ...tasking,\n                                                                        sarop: \"\",\n                                                                        cgop: e.target.value\n                                                                    }), setCurrentIncident(\"cgop\");\n                                                                },\n                                                                value: (_getPreviousCGOP = getPreviousCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop)) !== null && _getPreviousCGOP !== void 0 ? _getPreviousCGOP : \"\",\n                                                                \"aria-describedby\": \"cgop-error\",\n                                                                required: true,\n                                                                placeholder: \"Police / RCCNZ number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                                lineNumber: 2061,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                            lineNumber: 2059,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 2033,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                            htmlFor: \"task-sarop\",\n                                                            label: \"SAROP\",\n                                                            leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                                                id: \"task-sarop\",\n                                                                isRadioStyle: true,\n                                                                size: \"lg\",\n                                                                checked: getIsSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop),\n                                                                onCheckedChange: (checked)=>{\n                                                                    if (checked) handleSaropChange({\n                                                                        target: {\n                                                                            value: \"on\"\n                                                                        }\n                                                                    });\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                                lineNumber: 2090,\n                                                                columnNumber: 49\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                            lineNumber: 2086,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                                                id: \"sarop\",\n                                                                type: \"text\",\n                                                                onChange: (e)=>{\n                                                                    setTasking({\n                                                                        ...tasking,\n                                                                        sarop: e.target.value,\n                                                                        cgop: \"\"\n                                                                    }), setCurrentIncident(\"sarop\");\n                                                                },\n                                                                value: (_getPreviousSAROP = getPreviousSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop)) !== null && _getPreviousSAROP !== void 0 ? _getPreviousSAROP : \"\",\n                                                                \"aria-describedby\": \"sarop-error\",\n                                                                required: true,\n                                                                placeholder: \"Police / RCCNZ number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                                lineNumber: 2113,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                            lineNumber: 2111,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 2085,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 2032,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 2026,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2020,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2019,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_31__.Button, {\n                                variant: \"back\",\n                                onClick: ()=>closeModal(),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2144,\n                                columnNumber: 21\n                            }, this),\n                            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_31__.Button, {\n                                onClick: ()=>handleSave(0, 0),\n                                children: selectedEvent ? \"Update\" : \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2148,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2143,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2017,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2162,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2161,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                            id: \"parent-location\",\n                            options: locations || [],\n                            onChange: handleParentLocationChange,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2171,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2170,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2180,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2179,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2190,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2189,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2155,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_risk_analysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                offline: offline,\n                selectedEvent: selectedEvent,\n                crewMembers: members,\n                towingChecklistID: towingChecklistID,\n                setTowingChecklistID: setTowingChecklistID,\n                setAllChecked: setAllChecked,\n                onSidebarClose: ()=>setOpenRiskAnalysis(false),\n                logBookConfig: undefined,\n                currentTrip: currentTrip,\n                open: openRiskAnalysis,\n                onOpenChange: setOpenRiskAnalysis\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2201,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n        lineNumber: 1650,\n        columnNumber: 9\n    }, this);\n}\n_s(Tasking, \"GFJkr1zSemheuoxajglNxR7bBSE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_34__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery\n    ];\n});\n_c = Tasking;\nvar _c;\n$RefreshReg$(_c, \"Tasking\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\n"));

/***/ })

});
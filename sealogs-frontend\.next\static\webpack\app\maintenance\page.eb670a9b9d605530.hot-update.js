"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-cf.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlare; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/image.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_file_upload_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/file-upload-ui */ \"(app-pages-browser)/./src/components/ui/file-upload-ui.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_3___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlare(param) {\n    let { files = [], setFiles, multipleUpload = true, text = \"Documents and Images\", subText, bgClass = \"\", accept = \".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv\", bucketName = \"sealogs\", prefix = \"\", displayFiles = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state                                                   */ /* ------------------------------------------------------- */ const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    const uploadFile = async (file)=>{\n        const fileName = clientID + \"-\" + prefix + file.name;\n        const isFileExists = files === null || files === void 0 ? void 0 : files.some((existingFile)=>existingFile.title === fileName);\n        if (isFileExists) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"File with same name already exists!\");\n            return;\n        }\n        setImageLoader(true);\n        // Upload file to Cloudflare\n        s3Client.putObject({\n            Bucket: bucketName,\n            Key: fileName,\n            Body: file\n        }, (err, _data)=>{\n            setImageLoader(false);\n            if (err) {\n                console.error(err);\n                (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                    description: \"Failed to upload file\",\n                    variant: \"destructive\"\n                });\n            } else {\n                const newFile = {\n                    title: fileName\n                };\n                if (multipleUpload) {\n                    setFiles((prevState)=>[\n                            ...prevState,\n                            newFile\n                        ]);\n                } else {\n                    setFiles([\n                        newFile\n                    ]);\n                }\n            }\n        });\n    };\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFilesSelected = (fileList)=>{\n        const arr = Array.from(fileList);\n        arr.forEach(uploadFile);\n    };\n    const handleFileClick = (file)=>{\n        // Download file from Cloudflare\n        s3Client.getObject({\n            Bucket: bucketName,\n            Key: file.title\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                    description: \"Failed to download file\",\n                    variant: \"destructive\"\n                });\n            } else {\n                const fileType = file.title.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    data === null || data === void 0 ? void 0 : data.Body\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    setImage(url);\n                    setDisplayImage(true);\n                } else if (fileType.match(/^(pdf)$/i)) {\n                    const pdfBlob = new Blob([\n                        data === null || data === void 0 ? void 0 : data.Body\n                    ], {\n                        type: \"application/pdf\"\n                    });\n                    const pdfUrl = URL.createObjectURL(pdfBlob);\n                    window.open(pdfUrl, \"_blank\");\n                    URL.revokeObjectURL(pdfUrl);\n                } else {\n                    (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                        description: \"File type not supported to view. Please save the file to view.\",\n                        variant: \"destructive\"\n                    });\n                    const link = document.createElement(\"a\");\n                    link.target = \"_blank\";\n                    link.href = url;\n                    link.download = file.title;\n                    link.click();\n                    URL.revokeObjectURL(url);\n                }\n            }\n        });\n    };\n    /* ------------------------------------------------------- */ /* custom file item renderer                               */ /* ------------------------------------------------------- */ const renderFileItem = (file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>handleFileClick(file),\n            className: \"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: \"/sealogs-document_upload.svg\",\n                    alt: \"Document\",\n                    width: 48,\n                    height: 48,\n                    className: \"mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-center break-all text-muted-foreground\",\n                    children: file.title.replace(clientID + \"-\", \"\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, index, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n            lineNumber: 172,\n            columnNumber: 9\n        }, this);\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_file_upload_ui__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        files: files,\n        onFilesSelected: handleFilesSelected,\n        text: text,\n        subText: subText,\n        bgClass: bgClass,\n        multipleUpload: multipleUpload,\n        acceptedFileTypes: accept,\n        isLoading: imageLoader,\n        renderFileItem: renderFileItem,\n        displayFiles: displayFiles,\n        onFileClick: handleFileClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.AlertDialogNew, {\n            openDialog: displayImage,\n            setOpenDialog: setDisplayImage,\n            noButton: true,\n            actionText: \"Close\",\n            title: \"Image Preview\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: image,\n                    alt: \"Preview\",\n                    className: \"max-w-full max-h-96 object-contain\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                lineNumber: 212,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n            lineNumber: 206,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n        lineNumber: 193,\n        columnNumber: 9\n    }, this);\n}\n_s(UploadCloudFlare, \"faEDO8B1POrL2vuPmJxjFGBw5KQ=\");\n_c = UploadCloudFlare;\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlare\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\n"));

/***/ })

});
'use client'

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import {
    CreateRefuellingBunkering,
    UpdateRefuellingBunkering,
    CreateTripEvent,
    UpdateTripEvent,
    CREATE_FUELLOG,
    UPDATE_FUELLOG,
    UpdateFuelTank,
    CREATE_R2FILE,
} from '@/app/lib/graphQL/mutation'
import {
    GET_FUELLOGS,
    GET_FUELLOGS_BY_LBE,
    GET_FUELTANKS,
    GetTripEvent,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import LocationField from '../components/location'
import { getVesselByID } from '@/app/lib/actions'
import { useSearchParams } from 'next/navigation'
import { SealogsFuelIcon } from '@/app/lib/icons/SealogsFuelIcon'
import FuelTankModel from '@/app/offline/models/fuelTank'
import VesselModel from '@/app/offline/models/vessel'
import TripEventModel from '@/app/offline/models/tripEvent'
import RefuellingBunkeringModel from '@/app/offline/models/refuellingBunkering'
import FuelLogModel from '@/app/offline/models/fuelLog'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import TimeField from '../components/time'
import UploadCloudFlare from '../components/upload-cf'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { Check, ArrowLeft } from 'lucide-react'
import { AlertDialogNew, Separator } from '@/components/ui'

export default function RefuellingBunkering({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    logBookConfig,
    locked,
    offline = false,
    mainFuelLogs,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    logBookConfig: any
    locked: any
    offline?: boolean
    mainFuelLogs?: any
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [time, setTime] = useState<any>(dayjs())
    const [content, setContent] = useState<any>('')
    const [refuellingBunkering, setRefuellingBunkering] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [fuelLogs, setFuelLogs] = useState<any>([])
    const [fuelTankList, setFuelTankList] = useState<any>(false)
    const [updatedFuelLogs, setUpdatedFuelLogs] = useState<any>([])
    const [fuelReceipts, setFuelReceipts] = useState<any>([])
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [displayFuelUsed, setDisplayFuelUsed] = useState<any>(false)
    const [selectedFuelTank, setSelectedFuelTank] = useState<any>(false)
    const [fuelUsed, setFuelUsed] = useState<any>([])
    const [LBEFuelLogs, setLBEFuelLogs] = useState<any>([])

    const fuelTankModel = new FuelTankModel()
    const vesselModel = new VesselModel()
    const tripEventModel = new TripEventModel()
    const refuellingBunkeringModel = new RefuellingBunkeringModel()
    const fuelLogModel = new FuelLogModel()
    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            setFuelTankList(data)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        if (offline) {
            // queryGetFuelTanks
            const data = await fuelTankModel.getByIds(fuelTankIds)
            setFuelTankList(data)
        } else {
            await queryGetFuelTanks({
                variables: {
                    id: fuelTankIds,
                },
            })
        }
    }

    const handleSetVessel = (data: any) => {
        const fuelTankIds = data?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
    }

    if (!offline) {
        getVesselByID(+vesselID, handleSetVessel)
    }
    const offlineUseEffect = async () => {
        // getVesselByID(+vesselID, handleSetVessel)
        const data = await vesselModel.getById(vesselID)
        handleSetVessel(data)
    }
    useEffect(() => {
        if (offline) {
            offlineUseEffect()
        }
    }, [offline])

    const handleTimeChange = (date: any) => {
        setTime(dayjs(date))
    }

    useEffect(() => {
        setRefuellingBunkering(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setRefuellingBunkering(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getFuelLogs = async (fuelLogIds: any) => {
        if (
            !fuelLogIds ||
            !Array.isArray(fuelLogIds) ||
            fuelLogIds.length === 0
        ) {
            return // Exit early if no valid IDs are provided
        }

        if (offline) {
            const data = await fuelTankModel.getByIds(fuelLogIds)
        } else {
            await queryGetFuelLogs({
                variables: {
                    id: fuelLogIds,
                },
            })
        }
    }

    const [queryGetFuelLogs] = useLazyQuery(GET_FUELLOGS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelLogs.nodes
            setUpdatedFuelLogs(data)
        },
        onError: (error: any) => {
            console.error('getFuelLogs error', error)
        },
    })

    const [queryGetFuelLogsByLBE] = useLazyQuery(GET_FUELLOGS_BY_LBE, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelLogs.nodes
            setLBEFuelLogs(data)
        },
        onError: (error: any) => {
            console.error('getFuelLogs error', error)
        },
    })

    useEffect(() => {
        queryGetFuelLogsByLBE({
            variables: {
                filter: {
                    logBookEntryID: {
                        in: [+logentryID],
                    },
                    type: {
                        eq: 'FuelUsed',
                    },
                },
            },
        })
    }, [])

    useEffect(() => {
        if (
            mainFuelLogs &&
            Array.isArray(mainFuelLogs) &&
            mainFuelLogs.length > 0
        ) {
            getFuelLogs(mainFuelLogs.map((item: any) => item.id))
        }
    }, [mainFuelLogs])

    useEffect(() => {
        if (fuelReceipts.length > 0) {
            fuelReceipts.map((receipt: any) => {
                if (
                    !receipt.id &&
                    tripEvent?.eventType_RefuellingBunkering?.id > 0
                ) {
                    createFuelReceipts({
                        variables: {
                            input: {
                                title: receipt.title,
                                refuellingBunkeringID:
                                    tripEvent.eventType_RefuellingBunkering.id,
                            },
                        },
                    })
                }
            })
        }
    }, [fuelReceipts])

    const [createFuelReceipts] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = fuelReceipts.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setFuelReceipts(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            // getTripEvent
            const event = await tripEventModel.getById(id)
            if (event) {
                setTripEvent(event)
                setRefuellingBunkering({
                    geoLocationID:
                        event.eventType_RefuellingBunkering?.geoLocationID,
                    date: event.eventType_RefuellingBunkering?.date,
                    notes: event.eventType_RefuellingBunkering?.notes,
                    lat: event.eventType_RefuellingBunkering?.lat,
                    long: event.eventType_RefuellingBunkering?.long,
                })
                if (
                    event.eventType_RefuellingBunkering?.lat &&
                    event.eventType_RefuellingBunkering?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_RefuellingBunkering?.lat,
                        longitude: event.eventType_RefuellingBunkering?.long,
                    })
                }
                setContent(event.eventType_RefuellingBunkering?.notes)
                setTime(dayjs(event.eventType_RefuellingBunkering?.date))
                setFuelLogs(event.eventType_RefuellingBunkering.fuelLog.nodes)
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setRefuellingBunkering({
                    geoLocationID:
                        event.eventType_RefuellingBunkering?.geoLocationID,
                    date: event.eventType_RefuellingBunkering?.date,
                    notes: event.eventType_RefuellingBunkering?.notes,
                    lat: event.eventType_RefuellingBunkering?.lat,
                    long: event.eventType_RefuellingBunkering?.long,
                })
                if (
                    event.eventType_RefuellingBunkering?.lat &&
                    event.eventType_RefuellingBunkering?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_RefuellingBunkering?.lat,
                        longitude: event.eventType_RefuellingBunkering?.long,
                    })
                }
                setContent(event.eventType_RefuellingBunkering?.notes)
                setTime(dayjs(event.eventType_RefuellingBunkering?.date))
                setFuelLogs(event.eventType_RefuellingBunkering.fuelLog.nodes)
                setFuelReceipts(
                    event.eventType_RefuellingBunkering.fuelReceipts.nodes,
                )
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSave = async () => {
        const variables = {
            input: {
                geoLocationID: refuellingBunkering?.geoLocationID,
                notes: content,
                lat: currentLocation.latitude.toString(),
                date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                long: currentLocation.longitude.toString(),
            },
        }

        if (currentEvent) {
            if (offline) {
                // updateTripEvent
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'RefuellingBunkering',
                    logBookEntrySectionID: currentTrip.id,
                })
                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'RefuellingBunkering',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
            if (offline) {
                // updateRefuellingBunkering
                const data = await refuellingBunkeringModel.save({
                    id: +selectedEvent?.eventType_RefuellingBunkering?.id,
                    ...variables.input,
                })
                Promise.all(
                    fuelLogs.map(async (item: any) => {
                        if (item.id > 0) {
                            // updateFuelLog
                            await fuelLogModel.save({
                                id: item.id,
                                fuelTankID: +item.fuelTank.id,
                                fuelBefore: +item.fuelBefore,
                                fuelAdded: +item.fuelAdded,
                                fuelAfter: +item.fuelAfter,
                                date: dayjs(time).format(
                                    'YYYY-MM-DDTHH:mm:ssZ',
                                ),
                                refuellingBunkeringID: data.id,
                            })
                        } else {
                            // creteFuelLog
                            await fuelLogModel.save({
                                id: generateUniqueId(),
                                fuelTankID: +item.fuelTank.id,
                                fuelBefore: +item.fuelBefore,
                                fuelAdded: +item.fuelAdded,
                                fuelAfter: +item.fuelAfter,
                                date: dayjs(time).format(
                                    'YYYY-MM-DDTHH:mm:ssZ',
                                ),
                                refuellingBunkeringID: data.id,
                            })
                        }
                        // updateFuelTank
                        await fuelTankModel.save({
                            id: item.fuelTank.id,
                            currentLevel: +item.fuelAfter,
                        })
                    }),
                )
            } else {
                updateRefuellingBunkering({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_RefuellingBunkering
                                ?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                // createTripEvent
                const data = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'RefuellingBunkering',
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(data)
                // createRefuellingBunkering
                const refuellingBunkeringData =
                    await refuellingBunkeringModel.save({
                        id: generateUniqueId(),
                        geoLocationID: refuellingBunkering?.geoLocationID,
                        notes: content,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                        date: dayjs(time).format('YYYY-MM-DDTHH:mm:ssZ'),
                    })
                Promise.all(
                    fuelLogs.map(async (item: any) => {
                        // creteFuelLog
                        await fuelLogModel.save({
                            id: generateUniqueId(),
                            fuelTankID: +item.fuelTank.id,
                            fuelBefore: +item.fuelBefore,
                            fuelAdded: +item.fuelAdded,
                            fuelAfter: +item.fuelAfter,
                            date: dayjs(time).format('YYYY-MM-DDTHH:mm:ssZ'),
                            refuellingBunkeringID: refuellingBunkeringData.id,
                        })
                        // updateFuelTank
                        await fuelTankModel.save({
                            id: item.fuelTank.id,
                            currentLevel: +item.fuelAfter,
                        })
                    }),
                )
                setTimeout(async () => {
                    // updateTripEvent
                    const x = await tripEventModel.save({
                        id: currentEvent?.id,
                        eventType_RefuellingBunkeringID:
                            refuellingBunkeringData.id,
                    })
                    getCurrentEvent(currentEvent?.id)
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            currentTrip.id,
                        ],
                    })
                }, 200)
                closeModal()
                // updateTripEvent
                await tripEventModel.save({
                    id: data.id,
                    eventCategory: 'RefuellingBunkering',
                    eventType_RefuellingBunkeringID: refuellingBunkeringData.id,
                })
                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'RefuellingBunkering',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            createRefuellingBunkering({
                variables: {
                    input: {
                        geoLocationID: refuellingBunkering?.geoLocationID,
                        notes: content,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                        date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                    },
                },
            })
            // updateTripEvent({
            //     variables: {
            //         input: {
            //             id: data.id,
            //             eventCategory: 'RefuellingBunkering',
            //             eventType_RefuellingBunkeringID: data.id,
            //         },
            //     },
            // })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createRefuellingBunkering] = useMutation(CreateRefuellingBunkering, {
        onCompleted: (response) => {
            const data = response.createRefuellingBunkering
            if (fuelReceipts.length > 0) {
                fuelReceipts.map((receipt: any) => {
                    if (!receipt.id && data.id) {
                        createFuelReceipts({
                            variables: {
                                input: {
                                    title: receipt.title,
                                    refuellingBunkeringID: data.id,
                                },
                            },
                        })
                    }
                })
            }
            fuelLogs.map((item: any) => {
                creteFuelLog({
                    variables: {
                        input: {
                            fuelTankID: +item.fuelTank.id,
                            fuelBefore: +item.fuelBefore,
                            fuelAdded: +item.fuelAdded,
                            fuelAfter: +item.fuelAfter,
                            costPerLitre: +item.costPerLitre,
                            totalCost: +item.totalCost,
                            date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                            refuellingBunkeringID: data.id,
                        },
                    },
                })
                updateFuelTank({
                    variables: {
                        input: {
                            id: item.fuelTank.id,
                            currentLevel: +item.fuelAfter,
                        },
                    },
                })
            })
            const filteredLogs = LBEFuelLogs?.filter(
                (log: any) => log.typeID == 0,
            )
            filteredLogs.forEach((log: any) => {
                updateFuelLog({
                    variables: {
                        input: {
                            id: +log.id,
                            typeID: +data.id,
                        },
                    },
                })
            })
            setTimeout(() => {
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            eventType_RefuellingBunkeringID: data.id,
                        },
                    },
                })
            }, 200)
            closeModal()
        },
        onError: (error) => {
            console.error('Error creating refuelling', error)
        },
    })

    const [updateFuelTank] = useMutation(UpdateFuelTank, {
        onCompleted: (response) => {
            const data = response.updateFuelTank
            const fuelLog = updatedFuelLogs
                .filter((log: any) => log.fuelTank.id === data.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime(),
                )[0]
            if (fuelLog) {
                updateFuelLog({
                    variables: {
                        input: {
                            id: fuelLog.id,
                            fuelAfter: +fuelLog.fuelAfter,
                        },
                    },
                })
            }
        },
        onError: (error) => {
            console.error('Error updating fuel tank', error)
        },
    })

    const [creteFuelLog] = useMutation(CREATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.createFuelLog
        },
        onError: (error) => {
            console.error('Error creating fuel log', error)
        },
    })

    const [updateRefuellingBunkering] = useMutation(UpdateRefuellingBunkering, {
        onCompleted: (response) => {
            const data = response.updateRefuellingBunkering
            fuelLogs.map((item: any) => {
                if (item.id > 0) {
                    updateFuelLog({
                        variables: {
                            input: {
                                id: item.id,
                                fuelTankID: +item.fuelTank.id,
                                fuelBefore: +item.fuelBefore,
                                fuelAdded: +item.fuelAdded,
                                fuelAfter: +item.fuelAfter,
                                costPerLitre: +item.costPerLitre,
                                totalCost: +item.totalCost,
                                date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                                refuellingBunkeringID: data.id,
                            },
                        },
                    })
                } else {
                    creteFuelLog({
                        variables: {
                            input: {
                                fuelTankID: +item.fuelTank.id,
                                fuelBefore: +item.fuelBefore,
                                fuelAdded: +item.fuelAdded,
                                fuelAfter: +item.fuelAfter,
                                costPerLitre: +item.costPerLitre,
                                totalCost:
                                    +item.costPerLitre > 0 &&
                                    +item.fuelAdded > 0
                                        ? +item.costPerLitre * +item.fuelAdded
                                        : 0,
                                date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                                refuellingBunkeringID: data.id,
                            },
                        },
                    })
                    updateFuelTank({
                        variables: {
                            input: {
                                id: item.fuelTank.id,
                                currentLevel: +item.fuelAfter,
                            },
                        },
                    })
                }
            })
        },
        onError: (error) => {
            console.error('Error updating refuelling', error)
        },
    })

    const [updateFuelLog] = useMutation(UPDATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.updateFuelLog
        },
        onError: (error) => {
            console.error('Error updating fuel log', error)
        },
    })

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const displayField = (fieldName: string) => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            eventTypesConfig?.length > 0 &&
            eventTypesConfig[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, clear the location
        if (!value) {
            setRefuellingBunkering({
                ...refuellingBunkering,
                geoLocationID: 0,
                lat: null,
                long: null,
            })
            return
        }

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setRefuellingBunkering({
                ...refuellingBunkering,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, update currentLocation
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setCurrentLocation({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setRefuellingBunkering({
                ...refuellingBunkering,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update currentLocation
            setCurrentLocation({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }

    const getInitialFuelLevel = (tank: any) => {
        if (LBEFuelLogs.length > 0) {
            const eventTypeID = +selectedEvent?.eventType_RefuellingBunkering
                ?.id
                ? +selectedEvent?.eventType_RefuellingBunkering?.id
                : 0
            const filteredLogs = LBEFuelLogs.filter(
                (log: any) =>
                    log.fuelTank.id === tank.id && log.typeID == eventTypeID,
            )
            const lastLog =
                filteredLogs.length > 0
                    ? filteredLogs[filteredLogs.length - 1]
                    : undefined
            const fuelLevel = lastLog?.fuelAfter
            if (fuelLevel !== undefined) {
                return fuelLevel
            }
        }
        if (tripReport.length > 0) {
            const fuelLogs = tripReport
                .map((trip: any) => {
                    return trip.tripEvents.nodes
                        .filter(
                            (event: any) =>
                                (event.eventCategory === 'Tasking' &&
                                    event.eventType_Tasking.fuelLog.nodes
                                        .length > 0) ||
                                (event.eventCategory ===
                                    'RefuellingBunkering' &&
                                    event.eventType_RefuellingBunkering.fuelLog
                                        .nodes.length > 0) ||
                                (event.eventCategory ===
                                    'PassengerDropFacility' &&
                                    event.eventType_PassengerDropFacility
                                        .fuelLog.nodes.length > 0),
                        )
                        .flatMap(
                            (event: any) =>
                                (event.eventCategory === 'Tasking' &&
                                    event.eventType_Tasking.fuelLog.nodes) ||
                                (event.eventCategory ===
                                    'RefuellingBunkering' &&
                                    event.eventType_RefuellingBunkering.fuelLog
                                        .nodes) ||
                                (event.eventCategory ===
                                    'PassengerDropFacility' &&
                                    event.eventType_PassengerDropFacility
                                        .fuelLog.nodes),
                        )
                })
                .flat()
            const lastFuelLog = fuelLogs
                ?.filter((log: any) => log.fuelTank.id === tank.id)
                .sort((a: any, b: any) => b.id - a.id)?.[0]
            if (lastFuelLog) {
                return lastFuelLog.fuelAfter
            }
        }
        const fuelLog = updatedFuelLogs
            .filter((log: any) => log.fuelTank.id === tank.id)
            .sort(
                (a: any, b: any) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime(),
            )[0]
        return fuelLog
            ? +tank.capacity > +fuelLog.fuelAfter
                ? +fuelLog.fuelAfter
                : +tank.capacity
            : +tank.currentLevel
    }

    // const getPreviousFuelLevel = (tank: any) => {
    //     if (tripReport.length > 0) {
    //         const fuelLogs = tripReport
    //             .map((trip: any) => {
    //                 return trip.tripEvents.nodes
    //                     .filter(
    //                         (event: any) =>
    //                             event.eventCategory === 'Tasking' &&
    //                             event.eventType_Tasking.fuelLog.nodes.length >
    //                                 0,
    //                     )
    //                     .flatMap(
    //                         (event: any) =>
    //                             event.eventType_Tasking.fuelLog.nodes,
    //                     )
    //             })
    //             .flat()
    //         if (fuelLogs.length > 0) {
    //             return tank.currentLevel
    //         }
    //     }
    //     return false
    // }

    const getFuelBeforeRefuling = (tank: any) => {
        // if (getPreviousFuelLevel(tank)) {
        //     return getPreviousFuelLevel(tank)
        // }
        const fuelBefore = fuelLogs?.find(
            (item: any) => item.fuelTank.id == tank.id,
        )?.fuelBefore
            ? fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
                  .fuelBefore
            : getInitialFuelLevel(tank)

        // if (
        //     fuelUsed.length > 0 &&
        //     fuelUsed.find((item: any) => item.id == tank.id)?.fuelUsed &&
        //     displayFuelUsed == false
        // ) {
        //     return (
        //         +fuelBefore -
        //         fuelUsed.find((item: any) => item.id == tank.id)?.fuelUsed
        //     )
        // }
        return fuelBefore ?? undefined
    }

    const getFuelAdded = (tank: any) => {
        return (
            fuelLogs?.find((item: any) => item.fuelTank.id == tank.id)
                ?.fuelAdded ?? 0
        )
    }

    const getFuelAfterRefuling = (tank: any) => {
        // if (getPreviousFuelLevel(tank)) {
        //     return (
        //         getPreviousFuelLevel(tank) +
        //         +(
        //             fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
        //                 ?.fuelAdded ?? 0
        //         )
        //     )
        // }
        const fuelAfter = fuelLogs?.find(
            (item: any) => item.fuelTank.id == tank.id,
        )?.fuelAfter
            ? fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
                  .fuelAfter
            : getInitialFuelLevel(tank) +
              +(
                  fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
                      ?.fuelAdded ?? 0
              )

        // if (
        //     fuelUsed.length > 0 &&
        //     fuelUsed.find((item: any) => item.id == tank.id)?.fuelUsed &&
        //     displayFuelUsed == false
        // ) {
        //     return (
        //         +fuelAfter -
        //         fuelUsed.find((item: any) => item.id == tank.id)?.fuelUsed +
        //         +(
        //             fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
        //                 ?.fuelAdded ?? 0
        //         )
        //     )
        // }
        return fuelAfter ?? undefined
        // return (
        //     (fuelLogs?.find((item: any) => item.fuelTank.id == tank.id)
        //         ?.fuelAfter
        //         ? fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
        //               .fuelAfter
        //         : getInitialFuelLevel(tank) +
        //           +(
        //               fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
        //                   ?.fuelAdded ?? 0
        //           )) ?? undefined
        // )
    }

    const getFuelCost = (tank: any) => {
        return (
            fuelLogs?.find((item: any) => item.fuelTank.id == tank.id)
                ?.costPerLitre ?? 0
        )
    }

    const getFuelTotalCost = (tank: any) => {
        const costPerLitre =
            fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
                ?.costPerLitre ?? 0
        const fuelAdded =
            fuelLogs.find((item: any) => item.fuelTank.id == tank.id)
                ?.fuelAdded ?? 0
        if (costPerLitre > 0 && fuelAdded > 0) {
            const totalCost = costPerLitre * fuelAdded
            return parseFloat(totalCost.toFixed(2))
        }
        return fuelLogs?.find((item: any) => item.fuelTank.id == tank.id)
            ?.totalCost
            ? parseFloat(
                  fuelLogs
                      ?.find((item: any) => item.fuelTank.id == tank.id)
                      ?.totalCost.toFixed(2),
              )
            : 0
    }

    const getDaysInitialFuelLevel = (tank: any) => {
        const fuelLog = fuelLogs
            .filter((log: any) => log.fuelTank.id === tank.id)
            .sort(
                (a: any, b: any) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime(),
            )[0]
        return fuelLog
            ? +tank.capacity > +fuelLog.fuelBefore
                ? +fuelLog.fuelBefore
                : +tank.capacity
            : +tank.currentLevel
    }

    const handleFuelUsed = () => {
        if (selectedFuelTank) {
            const usedFuel =
                fuelUsed.find((item: any) => item.id == selectedFuelTank.id)
                    ?.fuelUsed ?? 0
            const fuelAfter = +getInitialFuelLevel(selectedFuelTank) - usedFuel
            creteFuelLog({
                variables: {
                    input: {
                        fuelTankID: +selectedFuelTank.id,
                        fuelBefore: +getInitialFuelLevel(selectedFuelTank),
                        fuelAfter: +fuelAfter,
                        date: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
                        logBookEntryID: +logentryID,
                        type: 'FuelUsed',
                        typeID: +selectedEvent?.eventType_RefuellingBunkering
                            ?.id,
                    },
                },
            })
            updateFuelTank({
                variables: {
                    input: {
                        id: selectedFuelTank.id,
                        currentLevel: +fuelAfter,
                    },
                },
            })
            setFuelLogs(
                fuelLogs.map((item: any) => {
                    if (item.fuelTank.id == selectedFuelTank.id) {
                        return {
                            ...item,
                            fuelBefore: +fuelAfter,
                        }
                    }
                    return item
                }),
            )
            setTimeout(() => {
                setDisplayFuelUsed(false)
                queryGetFuelLogsByLBE({
                    variables: {
                        filter: {
                            logBookEntryID: {
                                in: [+logentryID],
                            },
                            type: {
                                eq: 'FuelUsed',
                            },
                        },
                    },
                })
            }, 200)
        }
    }

    const getFuelAtEnd = (tank: any) => {
        const usedFuel =
            fuelUsed.find((item: any) => item.id == selectedFuelTank.id)
                ?.fuelUsed ?? 0
        return getInitialFuelLevel(selectedFuelTank) - usedFuel
    }

    return (
        <div className="space-y-8">
            <div className={`${locked ? 'pointer-events-none' : ''}`}>
                <Label
                    label="Location where Refuelling / Bunkering takes place"
                    htmlFor="refuelling-location">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={setCurrentLocation}
                        handleLocationChange={handleLocationChange}
                        currentEvent={tripEvent.eventType_RefuellingBunkering}
                    />
                </Label>
            </div>
            <div className={`${locked ? 'pointer-events-none' : ''}`}>
                <Label
                    label="Time when Refuelling / Bunkering takes place"
                    htmlFor="fuel-added-time">
                    <TimeField
                        time={
                            time
                                ? dayjs(time).format('HH:mm')
                                : dayjs().format('HH:mm')
                        }
                        handleTimeChange={handleTimeChange}
                        timeID="fuel-added-time"
                        fieldName="Time"
                    />
                </Label>
            </div>
            <div className={` ${locked ? 'pointer-events-none' : ''}`}>
                {fuelTankList &&
                    fuelTankList.length > 0 &&
                    fuelTankList.map((tank: any) => (
                        <div
                            className="flex flex-col sm:flex-row items-start sm:items-end sm:p-5 sm:border border-border border-dashed sm:rounded-lg"
                            key={tank.id}>
                            <Label
                                leftContent={
                                    <SealogsFuelIcon className="w-6 h-6 mb-0.5 text-gray-900" />
                                }
                                className="text-sm sm:mb-0 font-semibold uppercase w-48 mb-0">
                                {tank.title}
                            </Label>
                            <Separator className="my-2 sm:hidden" />
                            <div className="grid grid-cols-5 gap-2">
                                <Label
                                    label="Fuel before refuelling"
                                    className="justify-end"
                                    htmlFor={`fuel-level-${tank.id}`}>
                                    <Input
                                        id={`fuel-level-${tank.id}`}
                                        name={`fuel-level-${tank.id}`}
                                        type="number"
                                        min="0"
                                        max={tank.capacity}
                                        value={getFuelBeforeRefuling(tank)}
                                        placeholder="Fuel level"
                                        readOnly
                                        onClick={() => {
                                            if (
                                                selectedEvent
                                                    ?.eventType_RefuellingBunkering
                                                    ?.id == undefined
                                            ) {
                                                setDisplayFuelUsed(true)
                                                setSelectedFuelTank(tank)
                                            }
                                        }}
                                    />
                                </Label>
                                <Label
                                    label="Added fuel"
                                    className="justify-end"
                                    htmlFor={`fuel-added-${tank.id}`}>
                                    <Input
                                        id={`fuel-added-${tank.id}`}
                                        name={`fuel-added-${tank.id}`}
                                        type="number"
                                        min="0"
                                        max={
                                            tank.capacity -
                                            (fuelLogs.find(
                                                (item: any) =>
                                                    item.fuelTank.id == tank.id,
                                            )
                                                ? fuelLogs.find(
                                                      (item: any) =>
                                                          item.fuelTank.id ==
                                                          tank.id,
                                                  ).fuelBefore
                                                : getInitialFuelLevel(tank))
                                        }
                                        value={getFuelAdded(tank)}
                                        onChange={(e) => {
                                            if (
                                                +e.target.value >
                                                tank.capacity -
                                                    (fuelLogs.find(
                                                        (item: any) =>
                                                            item.fuelTank.id ==
                                                            tank.id,
                                                    )
                                                        ? fuelLogs.find(
                                                              (item: any) =>
                                                                  item.fuelTank
                                                                      .id ==
                                                                  tank.id,
                                                          ).fuelBefore
                                                        : getInitialFuelLevel(
                                                              tank,
                                                          ))
                                            ) {
                                                toast.error('Error', {
                                                    description:
                                                        'Fuel level cannot be more than fuel tank capacity of ' +
                                                        tank.capacity,
                                                })
                                                return
                                            }
                                            if (
                                                fuelLogs?.find(
                                                    (item: any) =>
                                                        item.fuelTank.id ==
                                                        tank.id,
                                                )
                                            ) {
                                                setFuelLogs(
                                                    fuelLogs.map(
                                                        (item: any) => {
                                                            if (
                                                                item.fuelTank
                                                                    .id ==
                                                                tank.id
                                                            ) {
                                                                return {
                                                                    ...item,
                                                                    fuelAdded:
                                                                        +e
                                                                            .target
                                                                            .value,
                                                                    fuelAfter:
                                                                        +item.fuelBefore +
                                                                        +e
                                                                            .target
                                                                            .value,
                                                                }
                                                            }
                                                            return item
                                                        },
                                                    ),
                                                )
                                            } else {
                                                setFuelLogs([
                                                    ...fuelLogs,
                                                    {
                                                        fuelTank: {
                                                            id: +tank.id,
                                                        },
                                                        fuelBefore:
                                                            getInitialFuelLevel(
                                                                tank,
                                                            ),
                                                        fuelAdded:
                                                            +e.target.value,
                                                        fuelAfter:
                                                            getInitialFuelLevel(
                                                                tank,
                                                            ) + +e.target.value,
                                                    },
                                                ])
                                            }
                                        }}
                                        placeholder="Fuel added"
                                    />
                                </Label>
                                <Label
                                    label="Current after refuelling"
                                    className="justify-end"
                                    htmlFor={`fuel-current-${tank.id}`}>
                                    <Input
                                        id={`fuel-current-${tank.id}`}
                                        name={`fuel-current-${tank.id}`}
                                        type="number"
                                        min={0}
                                        max={tank.capacity}
                                        value={getFuelAfterRefuling(tank)}
                                        readOnly
                                        placeholder="Current fuel"
                                    />
                                </Label>
                                <Label
                                    label="Cost per Litre"
                                    className="justify-end"
                                    htmlFor={`fuel-costpl-${tank.id}`}>
                                    <Input
                                        id={`fuel-costpl-${tank.id}`}
                                        name={`fuel-costpl-${tank.id}`}
                                        type="number"
                                        min={0}
                                        value={getFuelCost(tank)}
                                        onChange={(e) => {
                                            if (
                                                fuelLogs?.find(
                                                    (item: any) =>
                                                        item.fuelTank.id ==
                                                        tank.id,
                                                )
                                            ) {
                                                setFuelLogs(
                                                    fuelLogs.map(
                                                        (item: any) => {
                                                            if (
                                                                item.fuelTank
                                                                    .id ==
                                                                tank.id
                                                            ) {
                                                                return {
                                                                    ...item,
                                                                    costPerLitre:
                                                                        +e
                                                                            .target
                                                                            .value,
                                                                }
                                                            }
                                                            return item
                                                        },
                                                    ),
                                                )
                                            } else {
                                                setFuelLogs([
                                                    ...fuelLogs,
                                                    {
                                                        fuelTank: {
                                                            id: +tank.id,
                                                        },
                                                        costPerLitre:
                                                            +e.target.value,
                                                    },
                                                ])
                                            }
                                        }}
                                        placeholder="Cost per Litre"
                                    />
                                </Label>
                                <Label
                                    label="Total Cost"
                                    className="justify-end"
                                    htmlFor={`fuel-cost-${tank.id}`}>
                                    <Input
                                        id={`fuel-cost-${tank.id}`}
                                        name={`fuel-cost-${tank.id}`}
                                        type="number"
                                        min={0}
                                        value={getFuelTotalCost(tank)}
                                        readOnly
                                        placeholder="Total Cost"
                                    />
                                </Label>
                            </div>
                        </div>
                    ))}
            </div>
            <Label label="Fuel Receipts / Comments">
                <UploadCloudFlare
                    files={fuelReceipts}
                    setFiles={setFuelReceipts}
                />
            </Label>
            {(!currentEvent || refuellingBunkering) && (
                <Label label="Fuel Receipts comment" htmlFor="fuel-receipts">
                    <Textarea
                        disabled={locked}
                        id="fuel-receipts"
                        name="fuel-receipts"
                        rows={4}
                        placeholder="Fuel Receipts comment"
                        defaultValue={content}
                        onChange={(e) => handleEditorChange(e.target.value)}
                    />
                </Label>
            )}
            <div className="flex justify-end gap-2">
                <Button
                    variant="back"
                    iconLeft={ArrowLeft}
                    onClick={() => closeModal()}>
                    Cancel
                </Button>
                <Button
                    variant="primary"
                    iconLeft={Check}
                    onClick={locked ? () => {} : handleSave}>
                    {selectedEvent ? 'Update' : 'Save'}
                </Button>
            </div>
            <AlertDialogNew
                openDialog={displayFuelUsed}
                setOpenDialog={setDisplayFuelUsed}
                className="max-w-2xl"
                // size="xl"
                handleCreate={() => {
                    handleFuelUsed()
                }}
                title="Record fuel used"
                actionText="Save">
                <Label label={`Fuel at start`} htmlFor="fuel-used">
                    <Input
                        id="fuel-used"
                        name="fuel-used"
                        type="number"
                        min={0}
                        max={selectedFuelTank?.capacity}
                        value={getInitialFuelLevel(selectedFuelTank)}
                        readOnly
                        placeholder="Fuel used"
                    />
                </Label>{' '}
                <Label
                    label={`Fuel used in ${selectedFuelTank?.title}`}
                    htmlFor="fuel-used">
                    <Input
                        id="fuel-used"
                        name="fuel-used"
                        type="number"
                        min={0}
                        max={selectedFuelTank?.capacity}
                        value={
                            fuelUsed.find(
                                (item: any) => item.id == selectedFuelTank.id,
                            )
                                ? fuelUsed.find(
                                      (item: any) =>
                                          item.id == selectedFuelTank.id,
                                  ).fuelUsed
                                : 0
                        }
                        onChange={(e) => {
                            const existingFuelUsed = fuelUsed.find(
                                (item: any) => item.id == selectedFuelTank.id,
                            )
                            if (existingFuelUsed) {
                                setFuelUsed(
                                    fuelUsed.map((item: any) => {
                                        if (item.id == selectedFuelTank.id) {
                                            return {
                                                ...item,
                                                fuelUsed: +e.target.value,
                                            }
                                        }
                                        return item
                                    }),
                                )
                            } else {
                                setFuelUsed([
                                    ...fuelUsed,
                                    {
                                        id: selectedFuelTank.id,
                                        fuelUsed: +e.target.value,
                                    },
                                ])
                            }
                        }}
                        placeholder="Fuel used"
                    />
                </Label>
                <Label
                    label={`Fuel at end ${getFuelAtEnd(
                        selectedFuelTank,
                    )}`}></Label>
            </AlertDialogNew>
        </div>
    )
}

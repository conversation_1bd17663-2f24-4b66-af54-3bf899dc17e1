"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-cf.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlare; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/image.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_file_upload_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/file-upload-ui */ \"(app-pages-browser)/./src/components/ui/file-upload-ui.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_3___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlare(param) {\n    let { files = [], setFiles, multipleUpload = true, text = \"Documents and Images\", subText, bgClass = \"\", accept = \".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv\", bucketName = \"sealogs\", prefix = \"\", displayFiles = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state                                                   */ /* ------------------------------------------------------- */ const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    const uploadFile = async (file)=>{\n        const fileName = clientID + \"-\" + prefix + file.name;\n        const isFileExists = files === null || files === void 0 ? void 0 : files.some((existingFile)=>existingFile.title === fileName);\n        if (isFileExists) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"File with same name already exists!\");\n            return;\n        }\n        setImageLoader(true);\n        // Upload file to Cloudflare\n        s3Client.putObject({\n            Bucket: bucketName,\n            Key: fileName,\n            Body: file\n        }, (err, _data)=>{\n            setImageLoader(false);\n            if (err) {\n                console.error(err);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to upload file\");\n            } else {\n                const newFile = {\n                    title: fileName\n                };\n                if (multipleUpload) {\n                    setFiles((prevState)=>[\n                            ...prevState,\n                            newFile\n                        ]);\n                } else {\n                    setFiles([\n                        newFile\n                    ]);\n                }\n            }\n        });\n    };\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFilesSelected = (fileList)=>{\n        const arr = Array.from(fileList);\n        arr.forEach(uploadFile);\n    };\n    const handleFileClick = (file)=>{\n        // Download file from Cloudflare\n        s3Client.getObject({\n            Bucket: bucketName,\n            Key: file.title\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to download file\");\n            } else {\n                const fileType = file.title.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    data === null || data === void 0 ? void 0 : data.Body\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    setImage(url);\n                    setDisplayImage(true);\n                } else if (fileType.match(/^(pdf)$/i)) {\n                    const pdfBlob = new Blob([\n                        data === null || data === void 0 ? void 0 : data.Body\n                    ], {\n                        type: \"application/pdf\"\n                    });\n                    const pdfUrl = URL.createObjectURL(pdfBlob);\n                    window.open(pdfUrl, \"_blank\");\n                    URL.revokeObjectURL(pdfUrl);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"File type not supported to view. Please save the file to view.\");\n                    const link = document.createElement(\"a\");\n                    link.target = \"_blank\";\n                    link.href = url;\n                    link.download = file.title;\n                    link.click();\n                    URL.revokeObjectURL(url);\n                }\n            }\n        });\n    };\n    /* ------------------------------------------------------- */ /* custom file item renderer                               */ /* ------------------------------------------------------- */ const renderFileItem = (file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>handleFileClick(file),\n            className: \"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: \"/sealogs-document_upload.svg\",\n                    alt: \"Document\",\n                    width: 48,\n                    height: 48,\n                    className: \"mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-center break-all text-muted-foreground\",\n                    children: file.title.replace(clientID + \"-\", \"\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, index, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n            lineNumber: 164,\n            columnNumber: 9\n        }, this);\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_file_upload_ui__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        files: files,\n        onFilesSelected: handleFilesSelected,\n        text: text,\n        subText: subText,\n        bgClass: bgClass,\n        multipleUpload: multipleUpload,\n        acceptedFileTypes: accept,\n        isLoading: imageLoader,\n        renderFileItem: renderFileItem,\n        displayFiles: displayFiles,\n        onFileClick: handleFileClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.AlertDialogNew, {\n            openDialog: displayImage,\n            setOpenDialog: setDisplayImage,\n            noButton: true,\n            actionText: \"Close\",\n            title: \"Image Preview\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: image,\n                    alt: \"Preview\",\n                    className: \"max-w-full max-h-96 object-contain\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                lineNumber: 204,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n            lineNumber: 198,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n        lineNumber: 185,\n        columnNumber: 9\n    }, this);\n}\n_s(UploadCloudFlare, \"faEDO8B1POrL2vuPmJxjFGBw5KQ=\");\n_c = UploadCloudFlare;\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlare\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvbG9nYm9vay9jb21wb25lbnRzL3VwbG9hZC1jZi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQzBEO0FBQzVCO0FBQ0w7QUFFdUI7QUFDdUI7QUFDekM7QUFFOUIsTUFBTVEsYUFBYTtBQUNuQixNQUFNQyxnQkFBZ0I7QUFDdEIsTUFBTUMsb0JBQ0Y7QUFFSixNQUFNQyxXQUFXLElBQUlQLG1EQUFNLENBQUM7SUFDeEJTLFVBQVUsV0FBc0IsT0FBWEwsWUFBVztJQUNoQ00sYUFBYUw7SUFDYk0saUJBQWlCTDtJQUNqQk0sa0JBQWtCO0lBQ2xCQyxRQUFRO0FBQ1o7QUF5QmUsU0FBU0MsaUJBQWlCLEtBV2pCO1FBWGlCLEVBQ3JDQyxRQUFRLEVBQUUsRUFDVkMsUUFBUSxFQUNSQyxpQkFBaUIsSUFBSSxFQUNyQkMsT0FBTyxzQkFBc0IsRUFDN0JDLE9BQU8sRUFDUEMsVUFBVSxFQUFFLEVBQ1pDLFNBQVMsMkRBQTJELEVBQ3BFQyxhQUFhLFNBQVMsRUFDdEJDLFNBQVMsRUFBRSxFQUNYQyxlQUFlLElBQUksRUFDQyxHQVhpQjs7SUFZckMsMkRBQTJELEdBQzNELDJEQUEyRCxHQUMzRCwyREFBMkQsR0FDM0QsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUc1QiwrQ0FBUUEsQ0FBVTtJQUN4RCxNQUFNLENBQUM2QixPQUFPQyxTQUFTLEdBQUc5QiwrQ0FBUUEsQ0FBUztJQUMzQyxNQUFNLENBQUMrQixjQUFjQyxnQkFBZ0IsR0FBR2hDLCtDQUFRQSxDQUFVO0lBQzFELE1BQU0sQ0FBQ2lDLFVBQVVDLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFTO0lBRWpERCxnREFBU0EsQ0FBQztZQUNRb0M7UUFBZEQsWUFBWSxDQUFFQyxDQUFBQSxDQUFBQSx3QkFBQUEsYUFBYUMsT0FBTyxDQUFDLHlCQUFyQkQsbUNBQUFBLHdCQUFvQztJQUN0RCxHQUFHLEVBQUU7SUFFTCxNQUFNRSxhQUFhLE9BQU9DO1FBQ3RCLE1BQU1DLFdBQVdOLFdBQVcsTUFBTVIsU0FBU2EsS0FBS0UsSUFBSTtRQUNwRCxNQUFNQyxlQUFleEIsa0JBQUFBLDRCQUFBQSxNQUFPeUIsSUFBSSxDQUM1QixDQUFDQyxlQUFpQ0EsYUFBYUMsS0FBSyxLQUFLTDtRQUc3RCxJQUFJRSxjQUFjO1lBQ2RwQyx5Q0FBS0EsQ0FBQ3dDLEtBQUssQ0FBQztZQUNaO1FBQ0o7UUFFQWpCLGVBQWU7UUFFZiw0QkFBNEI7UUFDNUJuQixTQUFTcUMsU0FBUyxDQUNkO1lBQ0lDLFFBQVF2QjtZQUNSd0IsS0FBS1Q7WUFDTFUsTUFBTVg7UUFDVixHQUNBLENBQUNZLEtBQUtDO1lBQ0Z2QixlQUFlO1lBQ2YsSUFBSXNCLEtBQUs7Z0JBQ0xFLFFBQVFQLEtBQUssQ0FBQ0s7Z0JBQ2Q3Qyx5Q0FBS0EsQ0FBQ3dDLEtBQUssQ0FBQztZQUNoQixPQUFPO2dCQUNILE1BQU1RLFVBQTBCO29CQUFFVCxPQUFPTDtnQkFBUztnQkFDbEQsSUFBSXBCLGdCQUFnQjtvQkFDaEJELFNBQVMsQ0FBQ29DLFlBQWdDOytCQUNuQ0E7NEJBQ0hEO3lCQUNIO2dCQUNMLE9BQU87b0JBQ0huQyxTQUFTO3dCQUFDbUM7cUJBQVE7Z0JBQ3RCO1lBQ0o7UUFDSjtJQUVSO0lBRUEsMkRBQTJELEdBQzNELDJEQUEyRCxHQUMzRCwyREFBMkQsR0FDM0QsTUFBTUUsc0JBQXNCLENBQUNDO1FBQ3pCLE1BQU1DLE1BQU1DLE1BQU1DLElBQUksQ0FBQ0g7UUFDdkJDLElBQUlHLE9BQU8sQ0FBQ3ZCO0lBQ2hCO0lBRUEsTUFBTXdCLGtCQUFrQixDQUFDdkI7UUFDckIsZ0NBQWdDO1FBQ2hDN0IsU0FBU3FELFNBQVMsQ0FDZDtZQUNJZixRQUFRdkI7WUFDUndCLEtBQUtWLEtBQUtNLEtBQUs7UUFDbkIsR0FDQSxPQUFPTSxLQUFLYTtZQUNSLElBQUliLEtBQUs7Z0JBQ0xFLFFBQVFQLEtBQUssQ0FBQ0s7Z0JBQ2Q3Qyx5Q0FBS0EsQ0FBQ3dDLEtBQUssQ0FBQztZQUNoQixPQUFPO2dCQUNILE1BQU1tQixXQUFXMUIsS0FBS00sS0FBSyxDQUFDcUIsS0FBSyxDQUFDLEtBQUtDLEdBQUcsTUFBTTtnQkFDaEQsTUFBTUMsT0FBTyxJQUFJQyxLQUFLO29CQUFDTCxpQkFBQUEsMkJBQUFBLEtBQU1kLElBQUk7aUJBQWU7Z0JBQ2hELE1BQU1vQixNQUFNQyxJQUFJQyxlQUFlLENBQUNKO2dCQUVoQyxJQUFJSCxTQUFTUSxLQUFLLENBQUMsOEJBQThCO29CQUM3QzFDLFNBQVN1QztvQkFDVHJDLGdCQUFnQjtnQkFDcEIsT0FBTyxJQUFJZ0MsU0FBU1EsS0FBSyxDQUFDLGFBQWE7b0JBQ25DLE1BQU1DLFVBQVUsSUFBSUwsS0FBSzt3QkFBQ0wsaUJBQUFBLDJCQUFBQSxLQUFNZCxJQUFJO3FCQUFlLEVBQUU7d0JBQ2pEeUIsTUFBTTtvQkFDVjtvQkFDQSxNQUFNQyxTQUFTTCxJQUFJQyxlQUFlLENBQUNFO29CQUNuQ0csT0FBT0MsSUFBSSxDQUFDRixRQUFRO29CQUNwQkwsSUFBSVEsZUFBZSxDQUFDSDtnQkFDeEIsT0FBTztvQkFDSHRFLHlDQUFLQSxDQUFDd0MsS0FBSyxDQUNQO29CQUVKLE1BQU1rQyxPQUFPQyxTQUFTQyxhQUFhLENBQUM7b0JBQ3BDRixLQUFLRyxNQUFNLEdBQUc7b0JBQ2RILEtBQUtJLElBQUksR0FBR2Q7b0JBQ1pVLEtBQUtLLFFBQVEsR0FBRzlDLEtBQUtNLEtBQUs7b0JBQzFCbUMsS0FBS00sS0FBSztvQkFDVmYsSUFBSVEsZUFBZSxDQUFDVDtnQkFDeEI7WUFDSjtRQUNKO0lBRVI7SUFFQSwyREFBMkQsR0FDM0QsMkRBQTJELEdBQzNELDJEQUEyRCxHQUMzRCxNQUFNaUIsaUJBQWlCLENBQUNoRCxNQUFzQmlELHNCQUMxQyw4REFBQ0M7WUFFR0MsU0FBUyxJQUFNNUIsZ0JBQWdCdkI7WUFDL0JvRCxXQUFVOzs4QkFDViw4REFBQ3pGLGtEQUFLQTtvQkFDRjBGLEtBQUk7b0JBQ0pDLEtBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLFFBQVE7b0JBQ1JKLFdBQVU7Ozs7Ozs4QkFFZCw4REFBQ0Y7b0JBQUlFLFdBQVU7OEJBQ1ZwRCxLQUFLTSxLQUFLLENBQUNtRCxPQUFPLENBQUM5RCxXQUFXLEtBQUs7Ozs7Ozs7V0FYbkNzRDs7Ozs7SUFnQmIsMkRBQTJELEdBQzNELDJEQUEyRCxHQUMzRCwyREFBMkQsR0FDM0QscUJBQ0ksOERBQUNuRixxRUFBWUE7UUFDVGEsT0FBT0E7UUFDUCtFLGlCQUFpQnpDO1FBQ2pCbkMsTUFBTUE7UUFDTkMsU0FBU0E7UUFDVEMsU0FBU0E7UUFDVEgsZ0JBQWdCQTtRQUNoQjhFLG1CQUFtQjFFO1FBQ25CMkUsV0FBV3ZFO1FBQ1gyRCxnQkFBZ0JBO1FBQ2hCNUQsY0FBY0E7UUFDZHlFLGFBQWF0QztrQkFFYiw0RUFBQzFELDBEQUFjQTtZQUNYaUcsWUFBWXJFO1lBQ1pzRSxlQUFlckU7WUFDZnNFLFFBQVE7WUFDUkMsWUFBVztZQUNYM0QsT0FBTTtzQkFDTiw0RUFBQzRDO2dCQUFJRSxXQUFVOzBCQUNYLDRFQUFDYztvQkFDR2IsS0FBSzlEO29CQUNMK0QsS0FBSTtvQkFDSkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWxDO0dBeEt3QjFFO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvbG9nYm9vay9jb21wb25lbnRzL3VwbG9hZC1jZi50c3g/MmNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcclxuaW1wb3J0IEFXUyBmcm9tICdhd3Mtc2RrJ1xyXG5cclxuaW1wb3J0IHsgQWxlcnREaWFsb2dOZXcgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcbmltcG9ydCBGaWxlVXBsb2FkVUksIHsgQmFzZUZpbGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZmlsZS11cGxvYWQtdWknXHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJ1xyXG5cclxuY29uc3QgQUNDT1VOVF9JRCA9ICdkZGRlMWMxY2QxYWEyNTY0MTY5MTgwOGRjYmFmZGViNydcclxuY29uc3QgQUNDRVNTX0tFWV9JRCA9ICcwNmMzZTEzYTUzOWYyNGU2ZmRmNzA3NWJmMzgxYmY1ZSdcclxuY29uc3QgU0VDUkVUX0FDQ0VTU19LRVkgPVxyXG4gICAgJzBiYzIzZGI4NTU5NTA0ZmIzMDBiNTRkZWY1NjJkMDA3ZTRhMzczZmI5NDBhN2QwNzYxN2NlOTA2YzU1M2JiZTgnXHJcblxyXG5jb25zdCBzM0NsaWVudCA9IG5ldyBBV1MuUzMoe1xyXG4gICAgZW5kcG9pbnQ6IGBodHRwczovLyR7QUNDT1VOVF9JRH0ucjIuY2xvdWRmbGFyZXN0b3JhZ2UuY29tYCxcclxuICAgIGFjY2Vzc0tleUlkOiBBQ0NFU1NfS0VZX0lELFxyXG4gICAgc2VjcmV0QWNjZXNzS2V5OiBTRUNSRVRfQUNDRVNTX0tFWSxcclxuICAgIHNpZ25hdHVyZVZlcnNpb246ICd2NCcsXHJcbiAgICByZWdpb246ICdhdXRvJyxcclxufSlcclxuXHJcbi8vIFR5cGUgZGVmaW5pdGlvbnNcclxuaW50ZXJmYWNlIENsb3VkRmxhcmVGaWxlIGV4dGVuZHMgQmFzZUZpbGUge1xyXG4gICAgdGl0bGU6IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgVXBsb2FkQ2xvdWRGbGFyZVByb3BzIHtcclxuICAgIGZpbGVzPzogQ2xvdWRGbGFyZUZpbGVbXVxyXG4gICAgc2V0RmlsZXM6IChcclxuICAgICAgICBmaWxlczpcclxuICAgICAgICAgICAgfCBDbG91ZEZsYXJlRmlsZVtdXHJcbiAgICAgICAgICAgIHwgKChwcmV2OiBDbG91ZEZsYXJlRmlsZVtdKSA9PiBDbG91ZEZsYXJlRmlsZVtdKSxcclxuICAgICkgPT4gdm9pZFxyXG4gICAgbXVsdGlwbGVVcGxvYWQ/OiBib29sZWFuXHJcbiAgICB0ZXh0Pzogc3RyaW5nXHJcbiAgICBzdWJUZXh0Pzogc3RyaW5nXHJcbiAgICBiZ0NsYXNzPzogc3RyaW5nXHJcbiAgICBhY2NlcHQ/OiBzdHJpbmdcclxuICAgIGJ1Y2tldE5hbWU/OiBzdHJpbmdcclxuICAgIHByZWZpeD86IHN0cmluZ1xyXG4gICAgZGlzcGxheUZpbGVzPzogYm9vbGVhblxyXG4gICAgLy8gaGFuZGxlU2V0RmlsZT86IChmaWxlOiBDbG91ZEZsYXJlRmlsZVtdKSA9PiB2b2lkXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFVwbG9hZENsb3VkRmxhcmUoe1xyXG4gICAgZmlsZXMgPSBbXSxcclxuICAgIHNldEZpbGVzLFxyXG4gICAgbXVsdGlwbGVVcGxvYWQgPSB0cnVlLFxyXG4gICAgdGV4dCA9ICdEb2N1bWVudHMgYW5kIEltYWdlcycsXHJcbiAgICBzdWJUZXh0LFxyXG4gICAgYmdDbGFzcyA9ICcnLFxyXG4gICAgYWNjZXB0ID0gJy54bHN4LC54bHMsaW1hZ2UvKiwuZG9jLCAuZG9jeCwucHB0LCAucHB0eCwudHh0LC5wZGYsLmNzdicsXHJcbiAgICBidWNrZXROYW1lID0gJ3NlYWxvZ3MnLFxyXG4gICAgcHJlZml4ID0gJycsXHJcbiAgICBkaXNwbGF5RmlsZXMgPSB0cnVlLFxyXG59OiBVcGxvYWRDbG91ZEZsYXJlUHJvcHMpIHtcclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIC8qIHN0YXRlICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKi9cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIGNvbnN0IFtpbWFnZUxvYWRlciwgc2V0SW1hZ2VMb2FkZXJdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpXHJcbiAgICBjb25zdCBbaW1hZ2UsIHNldEltYWdlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpXHJcbiAgICBjb25zdCBbZGlzcGxheUltYWdlLCBzZXREaXNwbGF5SW1hZ2VdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpXHJcbiAgICBjb25zdCBbY2xpZW50SUQsIHNldENsaWVudElEXSA9IHVzZVN0YXRlPG51bWJlcj4oMClcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldENsaWVudElEKCsobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2NsaWVudElkJykgPz8gMCkpXHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICBjb25zdCB1cGxvYWRGaWxlID0gYXN5bmMgKGZpbGU6IEZpbGUpID0+IHtcclxuICAgICAgICBjb25zdCBmaWxlTmFtZSA9IGNsaWVudElEICsgJy0nICsgcHJlZml4ICsgZmlsZS5uYW1lXHJcbiAgICAgICAgY29uc3QgaXNGaWxlRXhpc3RzID0gZmlsZXM/LnNvbWUoXHJcbiAgICAgICAgICAgIChleGlzdGluZ0ZpbGU6IENsb3VkRmxhcmVGaWxlKSA9PiBleGlzdGluZ0ZpbGUudGl0bGUgPT09IGZpbGVOYW1lLFxyXG4gICAgICAgIClcclxuXHJcbiAgICAgICAgaWYgKGlzRmlsZUV4aXN0cykge1xyXG4gICAgICAgICAgICB0b2FzdC5lcnJvcignRmlsZSB3aXRoIHNhbWUgbmFtZSBhbHJlYWR5IGV4aXN0cyEnKVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHNldEltYWdlTG9hZGVyKHRydWUpXHJcblxyXG4gICAgICAgIC8vIFVwbG9hZCBmaWxlIHRvIENsb3VkZmxhcmVcclxuICAgICAgICBzM0NsaWVudC5wdXRPYmplY3QoXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIEJ1Y2tldDogYnVja2V0TmFtZSxcclxuICAgICAgICAgICAgICAgIEtleTogZmlsZU5hbWUsXHJcbiAgICAgICAgICAgICAgICBCb2R5OiBmaWxlLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAoZXJyLCBfZGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgc2V0SW1hZ2VMb2FkZXIoZmFsc2UpXHJcbiAgICAgICAgICAgICAgICBpZiAoZXJyKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnIpXHJcbiAgICAgICAgICAgICAgICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byB1cGxvYWQgZmlsZScpXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0ZpbGU6IENsb3VkRmxhcmVGaWxlID0geyB0aXRsZTogZmlsZU5hbWUgfVxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChtdWx0aXBsZVVwbG9hZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRGaWxlcygocHJldlN0YXRlOiBDbG91ZEZsYXJlRmlsZVtdKSA9PiBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2U3RhdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdGaWxlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEZpbGVzKFtuZXdGaWxlXSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgKVxyXG4gICAgfVxyXG5cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIC8qIGV2ZW50IGhhbmRsZXJzICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKi9cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIGNvbnN0IGhhbmRsZUZpbGVzU2VsZWN0ZWQgPSAoZmlsZUxpc3Q6IEZpbGVMaXN0KSA9PiB7XHJcbiAgICAgICAgY29uc3QgYXJyID0gQXJyYXkuZnJvbShmaWxlTGlzdClcclxuICAgICAgICBhcnIuZm9yRWFjaCh1cGxvYWRGaWxlKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZUZpbGVDbGljayA9IChmaWxlOiBDbG91ZEZsYXJlRmlsZSkgPT4ge1xyXG4gICAgICAgIC8vIERvd25sb2FkIGZpbGUgZnJvbSBDbG91ZGZsYXJlXHJcbiAgICAgICAgczNDbGllbnQuZ2V0T2JqZWN0KFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBCdWNrZXQ6IGJ1Y2tldE5hbWUsXHJcbiAgICAgICAgICAgICAgICBLZXk6IGZpbGUudGl0bGUsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGFzeW5jIChlcnIsIGRhdGEpID0+IHtcclxuICAgICAgICAgICAgICAgIGlmIChlcnIpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGVycilcclxuICAgICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIGRvd25sb2FkIGZpbGUnKVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWxlVHlwZSA9IGZpbGUudGl0bGUuc3BsaXQoJy4nKS5wb3AoKSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbZGF0YT8uQm9keSBhcyBVaW50OEFycmF5XSlcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChmaWxlVHlwZS5tYXRjaCgvXihqcGd8anBlZ3xwbmd8Z2lmfGJtcCkkL2kpKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEltYWdlKHVybClcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0RGlzcGxheUltYWdlKHRydWUpXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChmaWxlVHlwZS5tYXRjaCgvXihwZGYpJC9pKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwZGZCbG9iID0gbmV3IEJsb2IoW2RhdGE/LkJvZHkgYXMgVWludDhBcnJheV0sIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9wZGYnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwZGZVcmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKHBkZkJsb2IpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5vcGVuKHBkZlVybCwgJ19ibGFuaycpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwocGRmVXJsKVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ0ZpbGUgdHlwZSBub3Qgc3VwcG9ydGVkIHRvIHZpZXcuIFBsZWFzZSBzYXZlIHRoZSBmaWxlIHRvIHZpZXcuJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpbmsudGFyZ2V0ID0gJ19ibGFuaydcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGluay5ocmVmID0gdXJsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpbmsuZG93bmxvYWQgPSBmaWxlLnRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpbmsuY2xpY2soKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybClcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgKVxyXG4gICAgfVxyXG5cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIC8qIGN1c3RvbSBmaWxlIGl0ZW0gcmVuZGVyZXIgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKi9cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIGNvbnN0IHJlbmRlckZpbGVJdGVtID0gKGZpbGU6IENsb3VkRmxhcmVGaWxlLCBpbmRleDogbnVtYmVyKSA9PiAoXHJcbiAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVGaWxlQ2xpY2soZmlsZSl9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgY3Vyc29yLXBvaW50ZXIgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMjAgcC0yIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ib3JkZXIgaG92ZXI6Ym9yZGVyLXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnMgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgc3JjPVwiL3NlYWxvZ3MtZG9jdW1lbnRfdXBsb2FkLnN2Z1wiXHJcbiAgICAgICAgICAgICAgICBhbHQ9XCJEb2N1bWVudFwiXHJcbiAgICAgICAgICAgICAgICB3aWR0aD17NDh9XHJcbiAgICAgICAgICAgICAgICBoZWlnaHQ9ezQ4fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItMlwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWNlbnRlciBicmVhay1hbGwgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICB7ZmlsZS50aXRsZS5yZXBsYWNlKGNsaWVudElEICsgJy0nLCAnJyl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG5cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIC8qIHJlbmRlciAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKi9cclxuICAgIC8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gKi9cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPEZpbGVVcGxvYWRVSVxyXG4gICAgICAgICAgICBmaWxlcz17ZmlsZXN9XHJcbiAgICAgICAgICAgIG9uRmlsZXNTZWxlY3RlZD17aGFuZGxlRmlsZXNTZWxlY3RlZH1cclxuICAgICAgICAgICAgdGV4dD17dGV4dH1cclxuICAgICAgICAgICAgc3ViVGV4dD17c3ViVGV4dH1cclxuICAgICAgICAgICAgYmdDbGFzcz17YmdDbGFzc31cclxuICAgICAgICAgICAgbXVsdGlwbGVVcGxvYWQ9e211bHRpcGxlVXBsb2FkfVxyXG4gICAgICAgICAgICBhY2NlcHRlZEZpbGVUeXBlcz17YWNjZXB0fVxyXG4gICAgICAgICAgICBpc0xvYWRpbmc9e2ltYWdlTG9hZGVyfVxyXG4gICAgICAgICAgICByZW5kZXJGaWxlSXRlbT17cmVuZGVyRmlsZUl0ZW19XHJcbiAgICAgICAgICAgIGRpc3BsYXlGaWxlcz17ZGlzcGxheUZpbGVzfVxyXG4gICAgICAgICAgICBvbkZpbGVDbGljaz17aGFuZGxlRmlsZUNsaWNrfT5cclxuICAgICAgICAgICAgey8qIEltYWdlIHByZXZpZXcgZGlhbG9nICovfVxyXG4gICAgICAgICAgICA8QWxlcnREaWFsb2dOZXdcclxuICAgICAgICAgICAgICAgIG9wZW5EaWFsb2c9e2Rpc3BsYXlJbWFnZX1cclxuICAgICAgICAgICAgICAgIHNldE9wZW5EaWFsb2c9e3NldERpc3BsYXlJbWFnZX1cclxuICAgICAgICAgICAgICAgIG5vQnV0dG9uXHJcbiAgICAgICAgICAgICAgICBhY3Rpb25UZXh0PVwiQ2xvc2VcIlxyXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJJbWFnZSBQcmV2aWV3XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2ltYWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCJQcmV2aWV3XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXctZnVsbCBtYXgtaC05NiBvYmplY3QtY29udGFpblwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0FsZXJ0RGlhbG9nTmV3PlxyXG4gICAgICAgIDwvRmlsZVVwbG9hZFVJPlxyXG4gICAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiSW1hZ2UiLCJBV1MiLCJBbGVydERpYWxvZ05ldyIsIkZpbGVVcGxvYWRVSSIsInRvYXN0IiwiQUNDT1VOVF9JRCIsIkFDQ0VTU19LRVlfSUQiLCJTRUNSRVRfQUNDRVNTX0tFWSIsInMzQ2xpZW50IiwiUzMiLCJlbmRwb2ludCIsImFjY2Vzc0tleUlkIiwic2VjcmV0QWNjZXNzS2V5Iiwic2lnbmF0dXJlVmVyc2lvbiIsInJlZ2lvbiIsIlVwbG9hZENsb3VkRmxhcmUiLCJmaWxlcyIsInNldEZpbGVzIiwibXVsdGlwbGVVcGxvYWQiLCJ0ZXh0Iiwic3ViVGV4dCIsImJnQ2xhc3MiLCJhY2NlcHQiLCJidWNrZXROYW1lIiwicHJlZml4IiwiZGlzcGxheUZpbGVzIiwiaW1hZ2VMb2FkZXIiLCJzZXRJbWFnZUxvYWRlciIsImltYWdlIiwic2V0SW1hZ2UiLCJkaXNwbGF5SW1hZ2UiLCJzZXREaXNwbGF5SW1hZ2UiLCJjbGllbnRJRCIsInNldENsaWVudElEIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVwbG9hZEZpbGUiLCJmaWxlIiwiZmlsZU5hbWUiLCJuYW1lIiwiaXNGaWxlRXhpc3RzIiwic29tZSIsImV4aXN0aW5nRmlsZSIsInRpdGxlIiwiZXJyb3IiLCJwdXRPYmplY3QiLCJCdWNrZXQiLCJLZXkiLCJCb2R5IiwiZXJyIiwiX2RhdGEiLCJjb25zb2xlIiwibmV3RmlsZSIsInByZXZTdGF0ZSIsImhhbmRsZUZpbGVzU2VsZWN0ZWQiLCJmaWxlTGlzdCIsImFyciIsIkFycmF5IiwiZnJvbSIsImZvckVhY2giLCJoYW5kbGVGaWxlQ2xpY2siLCJnZXRPYmplY3QiLCJkYXRhIiwiZmlsZVR5cGUiLCJzcGxpdCIsInBvcCIsImJsb2IiLCJCbG9iIiwidXJsIiwiVVJMIiwiY3JlYXRlT2JqZWN0VVJMIiwibWF0Y2giLCJwZGZCbG9iIiwidHlwZSIsInBkZlVybCIsIndpbmRvdyIsIm9wZW4iLCJyZXZva2VPYmplY3RVUkwiLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwidGFyZ2V0IiwiaHJlZiIsImRvd25sb2FkIiwiY2xpY2siLCJyZW5kZXJGaWxlSXRlbSIsImluZGV4IiwiZGl2Iiwib25DbGljayIsImNsYXNzTmFtZSIsInNyYyIsImFsdCIsIndpZHRoIiwiaGVpZ2h0IiwicmVwbGFjZSIsIm9uRmlsZXNTZWxlY3RlZCIsImFjY2VwdGVkRmlsZVR5cGVzIiwiaXNMb2FkaW5nIiwib25GaWxlQ2xpY2siLCJvcGVuRGlhbG9nIiwic2V0T3BlbkRpYWxvZyIsIm5vQnV0dG9uIiwiYWN0aW9uVGV4dCIsImltZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\n"));

/***/ })

});
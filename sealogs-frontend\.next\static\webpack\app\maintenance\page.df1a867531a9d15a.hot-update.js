"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-cf.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlare; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/image.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_file_upload_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/file-upload-ui */ \"(app-pages-browser)/./src/components/ui/file-upload-ui.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_3___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlare(param) {\n    let { files = [], setFiles, multipleUpload = true, text = \"Documents and Images\", subText, bgClass = \"\", accept = \".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv\", bucketName = \"sealogs\", prefix = \"\", displayFiles = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state                                                   */ /* ------------------------------------------------------- */ const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    const uploadFile = async (file)=>{\n        const fileName = clientID + \"-\" + prefix + file.name;\n        const isFileExists = files === null || files === void 0 ? void 0 : files.some((existingFile)=>existingFile.title === fileName);\n        if (isFileExists) {\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                description: \"File with same name already exists!\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setImageLoader(true);\n        // Upload file to Cloudflare\n        s3Client.putObject({\n            Bucket: bucketName,\n            Key: fileName,\n            Body: file\n        }, (err, _data)=>{\n            setImageLoader(false);\n            if (err) {\n                console.error(err);\n                (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                    description: \"Failed to upload file\",\n                    variant: \"destructive\"\n                });\n            } else {\n                const newFile = {\n                    title: fileName\n                };\n                if (multipleUpload) {\n                    setFiles((prevState)=>[\n                            ...prevState,\n                            newFile\n                        ]);\n                } else {\n                    setFiles([\n                        newFile\n                    ]);\n                }\n            }\n        });\n    };\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFilesSelected = (fileList)=>{\n        const arr = Array.from(fileList);\n        arr.forEach(uploadFile);\n    };\n    const handleFileClick = (file)=>{\n        // Download file from Cloudflare\n        s3Client.getObject({\n            Bucket: bucketName,\n            Key: file.title\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                    description: \"Failed to download file\",\n                    variant: \"destructive\"\n                });\n            } else {\n                const fileType = file.title.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    data === null || data === void 0 ? void 0 : data.Body\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    setImage(url);\n                    setDisplayImage(true);\n                } else if (fileType.match(/^(pdf)$/i)) {\n                    const pdfBlob = new Blob([\n                        data === null || data === void 0 ? void 0 : data.Body\n                    ], {\n                        type: \"application/pdf\"\n                    });\n                    const pdfUrl = URL.createObjectURL(pdfBlob);\n                    window.open(pdfUrl, \"_blank\");\n                    URL.revokeObjectURL(pdfUrl);\n                } else {\n                    (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                        description: \"File type not supported to view. Please save the file to view.\",\n                        variant: \"destructive\"\n                    });\n                    const link = document.createElement(\"a\");\n                    link.target = \"_blank\";\n                    link.href = url;\n                    link.download = file.title;\n                    link.click();\n                    URL.revokeObjectURL(url);\n                }\n            }\n        });\n    };\n    /* ------------------------------------------------------- */ /* custom file item renderer                               */ /* ------------------------------------------------------- */ const renderFileItem = (file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>handleFileClick(file),\n            className: \"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: \"/sealogs-document_upload.svg\",\n                    alt: \"Document\",\n                    width: 48,\n                    height: 48,\n                    className: \"mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-center break-all text-muted-foreground\",\n                    children: file.title.replace(clientID + \"-\", \"\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, index, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n            lineNumber: 175,\n            columnNumber: 9\n        }, this);\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_file_upload_ui__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        files: files,\n        onFilesSelected: handleFilesSelected,\n        text: text,\n        subText: subText,\n        bgClass: bgClass,\n        multipleUpload: multipleUpload,\n        acceptedFileTypes: accept,\n        isLoading: imageLoader,\n        renderFileItem: renderFileItem,\n        displayFiles: displayFiles,\n        onFileClick: handleFileClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.AlertDialogNew, {\n            openDialog: displayImage,\n            setOpenDialog: setDisplayImage,\n            noButton: true,\n            actionText: \"Close\",\n            title: \"Image Preview\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: image,\n                    alt: \"Preview\",\n                    className: \"max-w-full max-h-96 object-contain\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n                lineNumber: 215,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n            lineNumber: 209,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-cf.tsx\",\n        lineNumber: 196,\n        columnNumber: 9\n    }, this);\n}\n_s(UploadCloudFlare, \"faEDO8B1POrL2vuPmJxjFGBw5KQ=\");\n_c = UploadCloudFlare;\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlare\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\n"));

/***/ })

});